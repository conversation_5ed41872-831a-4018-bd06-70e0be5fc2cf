/**
 * Security Protection CSS
 * Indonesian PDF Letter Generator
 * 
 * This CSS provides visual protection against:
 * - Text selection
 * - Image dragging
 * - Content highlighting
 * - Print styling
 * - Screenshot protection
 */

/* ========== GLOBAL SECURITY PROTECTION ========== */

/* Disable text selection and other interactions */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
}

/* Allow selection only for input fields and editable content */
input, 
textarea, 
[contenteditable="true"],
.selectable {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Disable image dragging and context menu */
img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: none;
    -webkit-touch-callout: none;
}

/* Allow interaction with clickable images */
img.clickable,
img[onclick],
a img {
    pointer-events: auto;
}

/* Disable highlighting and selection */
::selection {
    background: transparent;
}

::-moz-selection {
    background: transparent;
}

/* Disable text highlighting on mobile */
* {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
}

/* Anti-screenshot protection */
body {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Disable outline on focus for security */
*:focus {
    outline: none !important;
}

/* Custom focus styles for accessibility while maintaining security */
input:focus,
textarea:focus,
button:focus,
select:focus {
    outline: 2px solid #4F46E5 !important;
    outline-offset: 2px !important;
}

/* ========== PRINT PROTECTION ========== */

/* Disable printing completely */
@media print {
    * {
        display: none !important;
        visibility: hidden !important;
    }
    
    body {
        background: white !important;
    }
    
    body::before {
        content: "🛡️ PRINTING DISABLED FOR SECURITY REASONS 🛡️" !important;
        display: block !important;
        visibility: visible !important;
        font-size: 24px !important;
        font-weight: bold !important;
        text-align: center !important;
        margin-top: 100px !important;
        color: #ff0000 !important;
        font-family: Arial, sans-serif !important;
    }
    
    body::after {
        content: "This document contains sensitive information and cannot be printed." !important;
        display: block !important;
        visibility: visible !important;
        font-size: 16px !important;
        text-align: center !important;
        margin-top: 20px !important;
        color: #666 !important;
        font-family: Arial, sans-serif !important;
    }
}

/* ========== SCROLLBAR CUSTOMIZATION ========== */

/* Custom scrollbars to prevent inspection */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.5);
}

/* ========== MOBILE PROTECTION ========== */

/* Disable long press context menu on mobile */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Disable zoom on mobile */
input, textarea, select {
    font-size: 16px !important;
}

/* Prevent viewport manipulation */
@viewport {
    user-zoom: fixed;
    zoom: 1.0;
}

/* ========== CONTENT PROTECTION ========== */

/* Hide content when developer tools might be open */
@media (max-height: 500px) and (min-width: 800px) {
    .sensitive-content {
        filter: blur(5px);
        pointer-events: none;
    }
    
    .sensitive-content::after {
        content: "Content hidden for security reasons";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 1000;
    }
}

/* ========== FORM PROTECTION ========== */

/* Prevent autocomplete inspection */
input[type="password"] {
    -webkit-text-security: disc;
}

/* Hide form data from inspection */
.secure-form input:not(:focus) {
    color: transparent;
    text-shadow: 0 0 0 #000;
}

.secure-form input:focus {
    color: inherit;
    text-shadow: none;
}

/* ========== WATERMARK PROTECTION ========== */

/* Add subtle watermark to prevent screenshots */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 100px,
            rgba(255,255,255,0.01) 100px,
            rgba(255,255,255,0.01) 101px
        );
    pointer-events: none;
    z-index: 9999;
}

/* ========== ANIMATION PROTECTION ========== */

/* Disable animations when developer tools might be open */
@media (max-height: 500px) and (min-width: 800px) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ========== ACCESSIBILITY CONSIDERATIONS ========== */

/* Ensure screen readers can still access content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* ========== DEBUGGING PROTECTION ========== */

/* Hide elements that might reveal structure */
.debug-info,
.dev-tools,
.inspector-target {
    display: none !important;
    visibility: hidden !important;
}

/* Obfuscate class names and IDs in production */
[class*="debug"],
[id*="debug"],
[class*="dev"],
[id*="dev"] {
    display: none !important;
}

/* ========== RESPONSIVE SECURITY ========== */

/* Extra protection on larger screens where dev tools are more common */
@media (min-width: 1200px) {
    .extra-secure {
        position: relative;
    }
    
    .extra-secure::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        pointer-events: none;
        z-index: 1;
    }
}

/* ========== FINAL SECURITY LAYER ========== */

/* Ultimate protection against common inspection methods */
.protected-content {
    position: relative;
    overflow: hidden;
}

.protected-content::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: transparent;
    pointer-events: none;
    z-index: 1;
}

/* Disable common inspection shortcuts */
.no-inspect {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
    pointer-events: none;
}

.no-inspect input,
.no-inspect button,
.no-inspect a {
    pointer-events: auto;
}
