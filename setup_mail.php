<?php
echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup Mail Server - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-gray-100 min-h-screen py-8'>
    <div class='max-w-4xl mx-auto px-4'>
        <div class='bg-white rounded-lg shadow-lg p-8'>
            <h1 class='text-2xl font-bold text-gray-800 mb-6'>
                <i class='fas fa-envelope mr-3 text-blue-600'></i>
                Setup Mail Server untuk 2FA
            </h1>";

// Check current PHP mail configuration
$php_ini_path = php_ini_loaded_file();
$sendmail_path = ini_get('sendmail_path');
$smtp_server = ini_get('SMTP');
$smtp_port = ini_get('smtp_port');

echo "<div class='mb-8 p-6 bg-blue-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-blue-800 mb-4'>Current PHP Mail Configuration</h2>
        <div class='space-y-2 text-sm'>
            <p><strong>PHP.ini file:</strong> " . ($php_ini_path ?: 'Not found') . "</p>
            <p><strong>sendmail_path:</strong> " . ($sendmail_path ?: 'Not set') . "</p>
            <p><strong>SMTP:</strong> " . ($smtp_server ?: 'Not set') . "</p>
            <p><strong>smtp_port:</strong> " . ($smtp_port ?: 'Not set') . "</p>
            <p><strong>mail() function:</strong> " . (function_exists('mail') ? 'Available' : 'NOT Available') . "</p>
        </div>
      </div>";

// Solution 1: XAMPP Mercury Mail
echo "<div class='mb-8 p-6 bg-green-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-green-800 mb-4'>
            <i class='fas fa-rocket mr-2'></i>Solution 1: XAMPP Mercury Mail (Recommended)
        </h2>
        <div class='space-y-4'>
            <div class='bg-white p-4 rounded border'>
                <h3 class='font-medium mb-2'>Step 1: Enable Mercury in XAMPP</h3>
                <ol class='list-decimal list-inside text-sm space-y-1'>
                    <li>Buka XAMPP Control Panel</li>
                    <li>Klik 'Config' di sebelah Apache</li>
                    <li>Pilih 'PHP (php.ini)'</li>
                    <li>Cari baris yang mengandung 'sendmail_path'</li>
                    <li>Ubah menjadi: <code class='bg-gray-100 px-2 py-1 rounded'>sendmail_path = \"C:\\xampp\\sendmail\\sendmail.exe -t\"</code></li>
                    <li>Save file dan restart Apache</li>
                </ol>
            </div>
            
            <div class='bg-white p-4 rounded border'>
                <h3 class='font-medium mb-2'>Step 2: Configure Sendmail</h3>
                <ol class='list-decimal list-inside text-sm space-y-1'>
                    <li>Buka file: <code class='bg-gray-100 px-2 py-1 rounded'>C:\\xampp\\sendmail\\sendmail.ini</code></li>
                    <li>Edit konfigurasi berikut:</li>
                </ol>
                <div class='mt-2 bg-gray-100 p-3 rounded text-xs font-mono'>
smtp_server=smtp.gmail.com<br>
smtp_port=587<br>
smtp_ssl=tls<br>
auth_username=<EMAIL><br>
auth_password=your-app-password<br>
force_sender=<EMAIL>
                </div>
            </div>
        </div>
      </div>";

// Solution 2: PHPMailer
echo "<div class='mb-8 p-6 bg-yellow-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-yellow-800 mb-4'>
            <i class='fas fa-code mr-2'></i>Solution 2: PHPMailer Library
        </h2>
        <div class='space-y-4'>
            <p class='text-sm text-yellow-700'>Jika Mercury tidak bekerja, gunakan PHPMailer untuk SMTP.</p>
            
            <div class='bg-white p-4 rounded border'>
                <h3 class='font-medium mb-2'>Installation via Composer</h3>
                <div class='bg-gray-100 p-3 rounded text-sm font-mono'>
composer require phpmailer/phpmailer
                </div>
            </div>
            
            <div class='bg-white p-4 rounded border'>
                <h3 class='font-medium mb-2'>Manual Installation</h3>
                <ol class='list-decimal list-inside text-sm space-y-1'>
                    <li>Download PHPMailer dari GitHub</li>
                    <li>Extract ke folder <code class='bg-gray-100 px-2 py-1 rounded'>includes/phpmailer/</code></li>
                    <li>Include files yang diperlukan</li>
                </ol>
            </div>
        </div>
      </div>";

// Solution 3: Development Mail Catcher
echo "<div class='mb-8 p-6 bg-purple-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-purple-800 mb-4'>
            <i class='fas fa-bug mr-2'></i>Solution 3: Development Mail Catcher
        </h2>
        <div class='space-y-4'>
            <p class='text-sm text-purple-700'>Untuk development, gunakan mail catcher yang menangkap email tanpa mengirim.</p>
            
            <div class='bg-white p-4 rounded border'>
                <h3 class='font-medium mb-2'>MailHog (Recommended for Development)</h3>
                <ol class='list-decimal list-inside text-sm space-y-1'>
                    <li>Download MailHog dari GitHub</li>
                    <li>Jalankan MailHog.exe</li>
                    <li>Akses web interface di http://localhost:8025</li>
                    <li>Configure PHP untuk menggunakan MailHog SMTP</li>
                </ol>
            </div>
        </div>
      </div>";

// Quick Fix for Development
echo "<div class='mb-8 p-6 bg-red-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-red-800 mb-4'>
            <i class='fas fa-tools mr-2'></i>Quick Fix: File-based Email (Development Only)
        </h2>
        <div class='space-y-4'>
            <p class='text-sm text-red-700'>Untuk testing cepat, simpan email ke file instead of sending.</p>
            
            <button onclick='implementFileMail()' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700'>
                Implement File-based Email
            </button>
            
            <div id='fileMailResult' class='hidden mt-4 p-4 bg-white rounded border'></div>
        </div>
      </div>";

// Test Current Configuration
echo "<div class='mb-8 p-6 bg-gray-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-gray-800 mb-4'>
            <i class='fas fa-vial mr-2'></i>Test Current Configuration
        </h2>
        <div class='space-y-4'>
            <a href='debug-email' class='inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700'>
                <i class='fas fa-play mr-2'></i>Test Email Sending
            </a>
            
            <a href='test-2fa-flow' class='inline-block bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700'>
                <i class='fas fa-shield-alt mr-2'></i>Test 2FA Flow
            </a>
        </div>
      </div>";

// Links and Resources
echo "<div class='p-6 bg-indigo-50 rounded-lg'>
        <h2 class='text-lg font-semibold text-indigo-800 mb-4'>
            <i class='fas fa-link mr-2'></i>Useful Resources
        </h2>
        <div class='space-y-2 text-sm'>
            <p><a href='https://github.com/mailhog/MailHog' target='_blank' class='text-indigo-600 hover:underline'>MailHog - Email testing tool</a></p>
            <p><a href='https://github.com/PHPMailer/PHPMailer' target='_blank' class='text-indigo-600 hover:underline'>PHPMailer - SMTP library</a></p>
            <p><a href='https://mailtrap.io/' target='_blank' class='text-indigo-600 hover:underline'>Mailtrap - Email testing service</a></p>
            <p><a href='https://www.apachefriends.org/faq_windows.html' target='_blank' class='text-indigo-600 hover:underline'>XAMPP Mail Configuration Guide</a></p>
        </div>
      </div>";

echo "        </div>
    </div>

    <script>
    function implementFileMail() {
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'implement_file_mail=1'
        })
        .then(response => response.text())
        .then(data => {
            document.getElementById('fileMailResult').innerHTML = data;
            document.getElementById('fileMailResult').classList.remove('hidden');
        });
    }
    </script>
</body>
</html>";

// Handle file-based email implementation
if (isset($_POST['implement_file_mail'])) {
    $file_mail_code = '<?php
// File-based email for development
function sendFileEmail($to, $subject, $message, $headers = "") {
    $email_dir = "emails/";
    if (!is_dir($email_dir)) {
        mkdir($email_dir, 0777, true);
    }
    
    $filename = $email_dir . "email_" . date("Y-m-d_H-i-s") . "_" . uniqid() . ".html";
    
    $email_content = "
    <html>
    <head><title>$subject</title></head>
    <body>
        <h2>Email Details</h2>
        <p><strong>To:</strong> $to</p>
        <p><strong>Subject:</strong> $subject</p>
        <p><strong>Headers:</strong> $headers</p>
        <hr>
        <div>$message</div>
    </body>
    </html>";
    
    file_put_contents($filename, $email_content);
    return $filename;
}
?>';
    
    if (file_put_contents('includes/file_mail.php', $file_mail_code)) {
        echo "<div class='text-green-700'>
                <i class='fas fa-check mr-2'></i>
                File-based email system created successfully!<br>
                <small>File: includes/file_mail.php</small><br>
                <small>Emails will be saved to: emails/ directory</small>
              </div>";
    } else {
        echo "<div class='text-red-700'>
                <i class='fas fa-times mr-2'></i>
                Failed to create file-based email system.
              </div>";
    }
    exit;
}
?>
