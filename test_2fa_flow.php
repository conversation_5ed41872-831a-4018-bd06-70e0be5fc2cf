<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/TwoFactorAuth.php';

$test_results = [];
$current_user = User::getCurrentUser();

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_user_login'])) {
        $email = $_POST['test_email'] ?? '';
        $password = $_POST['test_password'] ?? '';
        
        if (!empty($email) && !empty($password)) {
            try {
                $database = new Database();
                $conn = $database->getConnection();
                
                $stmt = $conn->prepare("
                    SELECT id, username, email, password_hash, full_name, role, is_active 
                    FROM users 
                    WHERE (username = ? OR email = ?) AND is_active = 1
                ");
                $stmt->execute([$email, $email]);
                $user_data = $stmt->fetch();
                
                if ($user_data && password_verify($password, $user_data['password_hash'])) {
                    $twoFA = new TwoFactorAuth();
                    $result = $twoFA->generateAndSendCode($user_data['id'], $user_data['email'], 'login');
                    
                    if ($result['success']) {
                        $test_results['login'] = [
                            'status' => 'success',
                            'message' => "Login berhasil! Kode 2FA dikirim ke: {$user_data['email']}"
                        ];
                        
                        // Set temporary session for testing
                        $_SESSION['test_2fa_user_id'] = $user_data['id'];
                        $_SESSION['test_2fa_email'] = $user_data['email'];
                        $_SESSION['test_2fa_timestamp'] = time();
                    } else {
                        $test_results['login'] = [
                            'status' => 'error',
                            'message' => 'Gagal mengirim kode 2FA: ' . $result['message']
                        ];
                    }
                } else {
                    $test_results['login'] = [
                        'status' => 'error',
                        'message' => 'Credentials tidak valid'
                    ];
                }
            } catch (Exception $e) {
                $test_results['login'] = [
                    'status' => 'error',
                    'message' => 'Error: ' . $e->getMessage()
                ];
            }
        }
    }
    
    if (isset($_POST['test_verify_code'])) {
        $code = $_POST['verification_code'] ?? '';
        
        if (isset($_SESSION['test_2fa_user_id']) && !empty($code)) {
            $twoFA = new TwoFactorAuth();
            $result = $twoFA->verifyCode($_SESSION['test_2fa_user_id'], $code, 'login');
            
            if ($result['success']) {
                $test_results['verify'] = [
                    'status' => 'success',
                    'message' => 'Kode verifikasi berhasil! Login flow completed.'
                ];
                
                // Clear test session
                unset($_SESSION['test_2fa_user_id']);
                unset($_SESSION['test_2fa_email']);
                unset($_SESSION['test_2fa_timestamp']);
            } else {
                $test_results['verify'] = [
                    'status' => 'error',
                    'message' => 'Verifikasi gagal: ' . $result['message']
                ];
            }
        } else {
            $test_results['verify'] = [
                'status' => 'error',
                'message' => 'Kode tidak valid atau session expired'
            ];
        }
    }
    
    if (isset($_POST['clear_test_session'])) {
        unset($_SESSION['test_2fa_user_id']);
        unset($_SESSION['test_2fa_email']);
        unset($_SESSION['test_2fa_timestamp']);
        $test_results['clear'] = [
            'status' => 'success',
            'message' => 'Test session cleared'
        ];
    }
}

// Check test session status
$has_test_session = isset($_SESSION['test_2fa_user_id']);
$test_session_email = $_SESSION['test_2fa_email'] ?? '';
$test_session_time = $_SESSION['test_2fa_timestamp'] ?? 0;
$session_remaining = $test_session_time > 0 ? max(0, 600 - (time() - $test_session_time)) : 0;
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 2FA Flow - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-vial mr-3 text-purple-600"></i>
                Test 2FA Flow - Mandatory Email Verification
            </h1>

            <!-- Current Status -->
            <div class="mb-8 p-6 bg-blue-50 rounded-lg">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">Current Status</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <p><strong>Logged In User:</strong> <?php echo $current_user ? $current_user['username'] : 'None'; ?></p>
                        <p><strong>User Role:</strong> <?php echo $current_user ? $current_user['role'] : 'N/A'; ?></p>
                    </div>
                    <div>
                        <p><strong>Test Session:</strong> <?php echo $has_test_session ? 'Active' : 'None'; ?></p>
                        <?php if ($has_test_session): ?>
                            <p><strong>Test Email:</strong> <?php echo htmlspecialchars($test_session_email); ?></p>
                            <p><strong>Time Remaining:</strong> <?php echo gmdate('i:s', $session_remaining); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <?php if (!empty($test_results)): ?>
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Test Results</h2>
                    <div class="space-y-3">
                        <?php foreach ($test_results as $test_name => $result): ?>
                            <div class="border rounded-lg p-4 <?php 
                                echo $result['status'] === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; 
                            ?>">
                                <div class="flex items-center">
                                    <i class="fas <?php 
                                        echo $result['status'] === 'success' ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'; 
                                    ?> mr-3"></i>
                                    <div>
                                        <h3 class="font-medium capitalize"><?php echo str_replace('_', ' ', $test_name); ?></h3>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($result['message']); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Step 1: Test Login -->
            <div class="mb-8 p-6 bg-yellow-50 rounded-lg">
                <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="fas fa-sign-in-alt mr-2"></i>Step 1: Test Login (Always Requires 2FA)
                </h2>
                
                <form method="POST" class="space-y-4">
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email/Username:</label>
                            <input type="text" name="test_email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password:</label>
                            <input type="password" name="test_password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500">
                        </div>
                    </div>
                    <button type="submit" name="test_user_login" 
                            class="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>Test Login Flow
                    </button>
                </form>
                
                <div class="mt-4 text-sm text-yellow-700">
                    <p><strong>Expected:</strong> Credentials akan divalidasi, kemudian kode 2FA dikirim ke email (SELALU, tanpa opsi trust device)</p>
                </div>
            </div>

            <!-- Step 2: Test Verification -->
            <?php if ($has_test_session): ?>
                <div class="mb-8 p-6 bg-green-50 rounded-lg">
                    <h2 class="text-lg font-semibold text-green-800 mb-4">
                        <i class="fas fa-shield-alt mr-2"></i>Step 2: Test Code Verification
                    </h2>
                    
                    <form method="POST" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">6-Digit Verification Code:</label>
                            <input type="text" name="verification_code" maxlength="6" pattern="\d{6}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500"
                                   placeholder="123456">
                        </div>
                        <div class="flex space-x-4">
                            <button type="submit" name="test_verify_code" 
                                    class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-check mr-2"></i>Verify Code
                            </button>
                            <button type="submit" name="clear_test_session" 
                                    class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-times mr-2"></i>Clear Session
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4 text-sm text-green-700">
                        <p><strong>Expected:</strong> Kode akan diverifikasi. Jika valid, login completed. Jika invalid, error dengan retry option.</p>
                        <p><strong>Session expires in:</strong> <?php echo gmdate('i:s', $session_remaining); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Flow Description -->
            <div class="mb-8 p-6 bg-purple-50 rounded-lg">
                <h2 class="text-lg font-semibold text-purple-800 mb-4">
                    <i class="fas fa-route mr-2"></i>New 2FA Flow Description
                </h2>
                <div class="space-y-3 text-sm text-purple-700">
                    <div class="flex items-start">
                        <span class="bg-purple-200 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">1</span>
                        <p><strong>User Login:</strong> User masukkan username/email + password</p>
                    </div>
                    <div class="flex items-start">
                        <span class="bg-purple-200 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">2</span>
                        <p><strong>Credential Check:</strong> System validasi credentials</p>
                    </div>
                    <div class="flex items-start">
                        <span class="bg-purple-200 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">3</span>
                        <p><strong>ALWAYS Send 2FA:</strong> Jika valid, SELALU kirim kode 6 digit ke email (tidak ada trusted device)</p>
                    </div>
                    <div class="flex items-start">
                        <span class="bg-purple-200 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">4</span>
                        <p><strong>Verify Code:</strong> User masukkan kode, system verify (max 3 attempts, 5 min expiry)</p>
                    </div>
                    <div class="flex items-start">
                        <span class="bg-purple-200 text-purple-800 px-2 py-1 rounded text-xs font-medium mr-3 mt-0.5">5</span>
                        <p><strong>Complete Login:</strong> Jika kode valid, complete login dan redirect ke dashboard/admin</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="text-center space-x-4">
                <a href="login" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>Real Login
                </a>
                <a href="admin/login" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-user-shield mr-2"></i>Admin Login
                </a>
                <a href="security-settings" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-shield-alt mr-2"></i>Security Settings
                </a>
                <a href="index" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>
