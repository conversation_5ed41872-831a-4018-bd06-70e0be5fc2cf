/* Custom styles for Generator Surat PDF */

/* Smooth animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

/* Custom scrollbar for preview */
#preview::-webkit-scrollbar {
  width: 8px;
}

#preview::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

#preview::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

#preview::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced form styling */
.form-input {
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Card shadows */
.card {
  transition: box-shadow 0.3s ease-in-out;
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Preview area styling */
#preview {
  font-family: "Courier New", monospace;
  line-height: 1.6;
  max-height: 500px;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  h1 {
    font-size: 2rem;
  }
}

/* Loading state */
.loading {
  pointer-events: none;
  opacity: 0.7;
}

/* File upload styling */
.file-upload {
  position: relative;
  overflow: hidden;
  display: inline-block;
  cursor: pointer;
}

.file-upload input[type="file"] {
  position: absolute;
  left: -9999px;
}

/* Image preview styling */
.image-preview {
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: border-color 0.2s ease-in-out;
}

.image-preview:hover {
  border-color: #3b82f6;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Success message */
.success-message {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  animation: slideUp 0.3s ease-out;
}

/* Error message */
.error-message {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  animation: slideUp 0.3s ease-out;
}

/* Additional improvements */
.form-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.preview-section {
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
}

/* Better spacing for mobile */
@media (max-width: 640px) {
  .container {
    padding: 0.5rem;
  }

  .card {
    padding: 1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  .btn-primary {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus states */
input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Disabled state */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fa-spin {
  animation: spin 1s linear infinite;
}
