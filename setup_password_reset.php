<?php
/**
 * Setup Password Reset Database Schema
 * Indonesian PDF Letter Generator
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup Password Reset - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-gray-100 min-h-screen py-8'>
    <div class='max-w-4xl mx-auto px-4'>
        <div class='bg-white rounded-lg shadow-lg p-8'>
            <h1 class='text-2xl font-bold text-gray-800 mb-6'>
                <i class='fas fa-key mr-3 text-green-600'></i>
                Setup Password Reset System
            </h1>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div class='space-y-4'>";
    
    // Read and execute SQL file
    $sql_file = 'database/password_reset_schema.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL commands
    $sql_commands = array_filter(array_map('trim', explode(';', $sql_content)));
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($sql_commands as $sql) {
        if (empty($sql) || strpos($sql, '--') === 0) {
            continue;
        }
        
        try {
            // Skip DELIMITER commands
            if (strpos($sql, 'DELIMITER') !== false) {
                continue;
            }
            
            // Handle CREATE EVENT separately (might need special privileges)
            if (strpos($sql, 'CREATE EVENT') !== false) {
                try {
                    $conn->exec($sql);
                    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                            <i class='fas fa-check mr-2'></i>
                            Created event: <strong>cleanup_expired_reset_tokens</strong>
                          </div>";
                } catch (PDOException $e) {
                    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>
                            <i class='fas fa-exclamation-triangle mr-2'></i>
                            Event creation skipped (requires EVENT privilege): cleanup_expired_reset_tokens
                          </div>";
                }
                continue;
            }
            
            $conn->exec($sql);
            $success_count++;
            
            // Show success for important operations
            if (strpos($sql, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE[^`]*`?([^`\s]+)`?/i', $sql, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                        <i class='fas fa-check mr-2'></i>
                        Created table: <strong>$table_name</strong>
                      </div>";
            } elseif (strpos($sql, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO[^`]*`?([^`\s]+)`?/i', $sql, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded'>
                        <i class='fas fa-plus mr-2'></i>
                        Inserted data into: <strong>$table_name</strong>
                      </div>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            $error_msg = $e->getMessage();
            
            if (strpos($error_msg, 'already exists') !== false) {
                echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>
                        <i class='fas fa-info-circle mr-2'></i>
                        Already exists (skipped)
                      </div>";
            } else {
                echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                        <i class='fas fa-times mr-2'></i>
                        Error: " . htmlspecialchars($error_msg) . "
                      </div>";
            }
        }
    }
    
    echo "</div>";
    
    // Summary
    echo "<div class='mt-8 p-6 bg-gray-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-gray-800 mb-4'>Setup Summary</h2>
            <div class='grid md:grid-cols-2 gap-4'>
                <div class='bg-green-100 p-4 rounded-lg'>
                    <div class='flex items-center'>
                        <i class='fas fa-check-circle text-green-600 text-2xl mr-3'></i>
                        <div>
                            <div class='font-semibold text-green-800'>Successful Operations</div>
                            <div class='text-green-600'>$success_count commands executed</div>
                        </div>
                    </div>
                </div>
                <div class='bg-red-100 p-4 rounded-lg'>
                    <div class='flex items-center'>
                        <i class='fas fa-exclamation-circle text-red-600 text-2xl mr-3'></i>
                        <div>
                            <div class='font-semibold text-red-800'>Errors</div>
                            <div class='text-red-600'>$error_count errors encountered</div>
                        </div>
                    </div>
                </div>
            </div>
          </div>";
    
    // Test Password Reset functionality
    echo "<div class='mt-8 p-6 bg-green-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-green-800 mb-4'>
                <i class='fas fa-vial mr-2'></i>Test Password Reset Setup
            </h2>";
    
    // Check if tables exist
    $tables_to_check = [
        'password_reset_tokens',
        'password_reset_logs'
    ];
    
    $all_tables_exist = true;
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='text-green-700 mb-2'>
                        <i class='fas fa-check mr-2'></i>Table <strong>$table</strong> exists
                      </div>";
            } else {
                echo "<div class='text-red-700 mb-2'>
                        <i class='fas fa-times mr-2'></i>Table <strong>$table</strong> missing
                      </div>";
                $all_tables_exist = false;
            }
        } catch (Exception $e) {
            echo "<div class='text-red-700 mb-2'>
                    <i class='fas fa-times mr-2'></i>Error checking table <strong>$table</strong>: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
            $all_tables_exist = false;
        }
    }
    
    // Test PasswordReset class
    try {
        require_once 'classes/PasswordReset.php';
        $passwordReset = new PasswordReset();
        echo "<div class='text-green-700 mb-2'>
                <i class='fas fa-check mr-2'></i>PasswordReset class loaded successfully
              </div>";
    } catch (Exception $e) {
        echo "<div class='text-red-700 mb-2'>
                <i class='fas fa-times mr-2'></i>Error loading PasswordReset class: " . htmlspecialchars($e->getMessage()) . "
              </div>";
        $all_tables_exist = false;
    }
    
    if ($all_tables_exist) {
        echo "<div class='mt-4 p-4 bg-green-100 border border-green-400 rounded-lg'>
                <div class='flex items-center'>
                    <i class='fas fa-thumbs-up text-green-600 text-xl mr-3'></i>
                    <div>
                        <div class='font-semibold text-green-800'>Password Reset Setup Complete!</div>
                        <div class='text-green-700'>All required tables and classes are ready.</div>
                    </div>
                </div>
              </div>";
    } else {
        echo "<div class='mt-4 p-4 bg-red-100 border border-red-400 rounded-lg'>
                <div class='flex items-center'>
                    <i class='fas fa-exclamation-triangle text-red-600 text-xl mr-3'></i>
                    <div>
                        <div class='font-semibold text-red-800'>Setup Incomplete</div>
                        <div class='text-red-700'>Some tables or classes are missing. Please check the errors above.</div>
                    </div>
                </div>
              </div>";
    }
    
    echo "</div>";
    
    // Next steps
    echo "<div class='mt-8 p-6 bg-blue-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-blue-800 mb-4'>
                <i class='fas fa-list-ol mr-2'></i>Next Steps
            </h2>
            <ol class='list-decimal list-inside space-y-2 text-blue-700'>
                <li>Test forgot password functionality</li>
                <li>Configure email settings for production</li>
                <li>Test complete password reset flow</li>
                <li>Monitor password reset logs</li>
                <li>Set up automatic cleanup of expired tokens</li>
            </ol>
          </div>";
    
    // Quick links
    echo "<div class='mt-8 text-center space-x-4'>
            <a href='forgot_password.php' class='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'>
                <i class='fas fa-key mr-2'></i>Test Forgot Password
            </a>
            <a href='login.php' class='bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors'>
                <i class='fas fa-sign-in-alt mr-2'></i>Login Page
            </a>
            <a href='index.php' class='bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors'>
                <i class='fas fa-home mr-2'></i>Home
            </a>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
            <i class='fas fa-exclamation-triangle mr-2'></i>
            <strong>Setup Failed:</strong> " . htmlspecialchars($e->getMessage()) . "
          </div>";
    
    echo "<div class='mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded'>
            <h3 class='font-semibold text-yellow-800 mb-2'>Troubleshooting:</h3>
            <ul class='list-disc list-inside text-yellow-700 space-y-1'>
                <li>Make sure your database connection is working</li>
                <li>Check if the database user has CREATE and ALTER privileges</li>
                <li>Verify that the SQL file exists: database/password_reset_schema.sql</li>
                <li>Check the error logs for more details</li>
            </ul>
          </div>";
}

echo "        </div>
    </div>
</body>
</html>";
?>
