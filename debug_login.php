<?php
session_start();
require_once 'config/database.php';
require_once 'classes/User.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug Login - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100 p-8'>
    <div class='max-w-2xl mx-auto'>
        <h1 class='text-2xl font-bold mb-6'>Debug Login System</h1>";

// Clear session if requested
if (isset($_GET['clear_session'])) {
    session_destroy();
    session_start();
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>
            Session cleared
          </div>";
}

// Session Information
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Session Information</h2>";

if (empty($_SESSION)) {
    echo "<p class='text-red-600'>No active session data</p>";
} else {
    echo "<table class='w-full text-sm'>
            <thead>
                <tr class='border-b'>
                    <th class='text-left p-2'>Key</th>
                    <th class='text-left p-2'>Value</th>
                </tr>
            </thead>
            <tbody>";
    
    foreach ($_SESSION as $key => $value) {
        $display_value = is_array($value) ? json_encode($value) : htmlspecialchars($value);
        echo "<tr class='border-b'>
                <td class='p-2 font-medium'>$key</td>
                <td class='p-2'>$display_value</td>
              </tr>";
    }
    echo "</tbody></table>";
}

echo "</div>";

// User Status
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>User Status</h2>";

$is_logged_in = User::isLoggedIn();
$current_user = User::getCurrentUser();

echo "<div class='space-y-2'>
        <p><strong>Is Logged In:</strong> <span class='" . ($is_logged_in ? 'text-green-600' : 'text-red-600') . "'>" . ($is_logged_in ? 'Yes' : 'No') . "</span></p>";

if ($current_user) {
    echo "<p><strong>Current User:</strong></p>
          <ul class='ml-4 space-y-1'>
            <li>ID: {$current_user['id']}</li>
            <li>Username: {$current_user['username']}</li>
            <li>Email: {$current_user['email']}</li>
            <li>Role: {$current_user['role']}</li>
          </ul>";
} else {
    echo "<p><strong>Current User:</strong> <span class='text-red-600'>None</span></p>";
}

echo "</div></div>";

// Test Login Form
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test Login</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_login'])) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (!empty($email) && !empty($password)) {
        try {
            $database = new Database();
            $conn = $database->getConnection();
            
            $stmt = $conn->prepare("
                SELECT id, username, email, password_hash, full_name, role, is_active 
                FROM users 
                WHERE (username = ? OR email = ?) AND is_active = 1
            ");
            $stmt->execute([$email, $email]);
            $user_data = $stmt->fetch();
            
            if ($user_data && password_verify($password, $user_data['password_hash'])) {
                // Manual login
                $_SESSION['logged_in'] = true;
                $_SESSION['user_id'] = $user_data['id'];
                $_SESSION['username'] = $user_data['username'];
                $_SESSION['email'] = $user_data['email'];
                $_SESSION['role'] = $user_data['role'];
                $_SESSION['full_name'] = $user_data['full_name'];
                
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
                        Login successful! Session updated.
                      </div>";
            } else {
                echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                        Invalid credentials
                      </div>";
            }
        } catch (Exception $e) {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                    Error: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
        }
    }
}

echo "<form method='POST' class='space-y-4'>
        <div>
            <label class='block text-sm font-medium text-gray-700 mb-1'>Email/Username:</label>
            <input type='text' name='email' class='w-full px-3 py-2 border border-gray-300 rounded-md' required>
        </div>
        <div>
            <label class='block text-sm font-medium text-gray-700 mb-1'>Password:</label>
            <input type='password' name='password' class='w-full px-3 py-2 border border-gray-300 rounded-md' required>
        </div>
        <button type='submit' name='test_login' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>
            Test Login
        </button>
      </form>";

echo "</div>";

// Actions
echo "<div class='bg-white rounded-lg shadow p-6'>
        <h2 class='text-lg font-semibold mb-4'>Actions</h2>
        <div class='space-x-4'>
            <a href='?clear_session=1' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700'>Clear Session</a>
            <a href='login' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>Go to Login</a>
            <a href='dashboard' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>Go to Dashboard</a>
            <a href='index' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'>Go to Index</a>
        </div>
      </div>";

// PHP Info
echo "<div class='mt-6 bg-gray-50 rounded-lg p-4'>
        <h3 class='font-semibold mb-2'>PHP Info</h3>
        <div class='text-sm space-y-1'>
            <p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>
            <p><strong>Session ID:</strong> " . session_id() . "</p>
            <p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>
            <p><strong>Current URL:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Unknown') . "</p>
            <p><strong>HTTP Host:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Unknown') . "</p>
        </div>
      </div>";

echo "    </div>
</body>
</html>";
?>
