<?php
require_once 'config/database.php';

class TwoFactorAuth
{
    private $conn;

    public function __construct($db = null)
    {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Generate and send 2FA code via email
     */
    public function generateAndSendCode($user_id, $email, $code_type = 'login')
    {
        try {
            // Generate 6-digit code
            $code = $this->generateCode();

            // Set expiry time (5 minutes from now)
            $expires_at = date('Y-m-d H:i:s', time() + 300);

            // Get user info
            $stmt = $this->conn->prepare("SELECT full_name FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();

            if (!$user) {
                throw new Exception('User not found');
            }

            // Invalidate previous codes for this user and type
            $this->invalidatePreviousCodes($user_id, $code_type);

            // Store code in database
            $stmt = $this->conn->prepare("
                INSERT INTO email_verification_codes 
                (user_id, email, verification_code, code_type, expires_at, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $user_id,
                $email,
                $code,
                $code_type,
                $expires_at,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Send email
            $this->sendCodeEmail($email, $code, $user['full_name'], $code_type);

            // Log the attempt
            $this->logLoginAttempt($email, '2fa_code', true, 'Code sent successfully');

            return [
                'success' => true,
                'message' => 'Kode verifikasi telah dikirim ke email Anda',
                'expires_in' => 300 // 5 minutes
            ];
        } catch (Exception $e) {
            error_log("2FA Code generation error: " . $e->getMessage());
            $this->logLoginAttempt($email, '2fa_code', false, $e->getMessage());

            return [
                'success' => false,
                'message' => 'Gagal mengirim kode verifikasi: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verify 2FA code
     */
    public function verifyCode($user_id, $code, $code_type = 'login')
    {
        try {
            // Get the latest valid code for this user
            $stmt = $this->conn->prepare("
                SELECT id, verification_code, expires_at, attempts, max_attempts, email
                FROM email_verification_codes 
                WHERE user_id = ? AND code_type = ? AND is_used = FALSE 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$user_id, $code_type]);
            $verification = $stmt->fetch();

            if (!$verification) {
                $this->logLoginAttempt('', '2fa_code', false, 'No valid code found');
                return [
                    'success' => false,
                    'message' => 'Kode verifikasi tidak ditemukan atau sudah digunakan'
                ];
            }

            // Check if code is expired
            if (strtotime($verification['expires_at']) < time()) {
                $this->markCodeAsUsed($verification['id']);
                $this->logLoginAttempt($verification['email'], '2fa_code', false, 'Code expired');
                return [
                    'success' => false,
                    'message' => 'Kode verifikasi sudah kedaluwarsa'
                ];
            }

            // Check attempts
            if ($verification['attempts'] >= $verification['max_attempts']) {
                $this->markCodeAsUsed($verification['id']);
                $this->logLoginAttempt($verification['email'], '2fa_code', false, 'Max attempts reached');
                return [
                    'success' => false,
                    'message' => 'Terlalu banyak percobaan. Silakan minta kode baru'
                ];
            }

            // Increment attempts
            $this->incrementAttempts($verification['id']);

            // Verify code
            if ($code === $verification['verification_code']) {
                // Mark code as used
                $this->markCodeAsUsed($verification['id']);
                $this->logLoginAttempt($verification['email'], '2fa_code', true, 'Code verified successfully');

                return [
                    'success' => true,
                    'message' => 'Kode verifikasi berhasil diverifikasi'
                ];
            } else {
                $this->logLoginAttempt($verification['email'], '2fa_code', false, 'Invalid code');
                return [
                    'success' => false,
                    'message' => 'Kode verifikasi tidak valid'
                ];
            }
        } catch (Exception $e) {
            error_log("2FA Code verification error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat memverifikasi kode'
            ];
        }
    }

    /**
     * Check if user has 2FA enabled
     */
    public function is2FAEnabled($user_id)
    {
        try {
            $stmt = $this->conn->prepare("SELECT two_factor_enabled FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();

            return $user ? (bool)$user['two_factor_enabled'] : false;
        } catch (Exception $e) {
            error_log("2FA status check error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Enable 2FA for user
     */
    public function enable2FA($user_id)
    {
        try {
            $stmt = $this->conn->prepare("UPDATE users SET two_factor_enabled = TRUE WHERE id = ?");
            $stmt->execute([$user_id]);

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("2FA enable error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Disable 2FA for user
     */
    public function disable2FA($user_id)
    {
        try {
            $stmt = $this->conn->prepare("
                UPDATE users 
                SET two_factor_enabled = FALSE, two_factor_secret = NULL 
                WHERE id = ?
            ");
            $stmt->execute([$user_id]);

            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("2FA disable error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if device is trusted
     */
    public function isTrustedDevice($user_id, $device_fingerprint)
    {
        try {
            $stmt = $this->conn->prepare("
                SELECT id FROM trusted_devices 
                WHERE user_id = ? AND device_fingerprint = ? 
                AND is_active = TRUE AND expires_at > NOW()
            ");
            $stmt->execute([$user_id, $device_fingerprint]);

            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            error_log("Trusted device check error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add trusted device
     */
    public function addTrustedDevice($user_id, $device_fingerprint, $device_name = null)
    {
        try {
            $expires_at = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)); // 30 days

            $stmt = $this->conn->prepare("
                INSERT INTO trusted_devices 
                (user_id, device_fingerprint, device_name, ip_address, user_agent, expires_at) 
                VALUES (?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                expires_at = VALUES(expires_at), 
                last_used_at = NOW(),
                is_active = TRUE
            ");

            $stmt->execute([
                $user_id,
                $device_fingerprint,
                $device_name,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                $expires_at
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Add trusted device error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate device fingerprint
     */
    public function generateDeviceFingerprint()
    {
        $components = [
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? ''
        ];

        return hash('sha256', implode('|', $components));
    }

    // Private helper methods

    private function generateCode($length = 6)
    {
        return str_pad(random_int(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }

    private function invalidatePreviousCodes($user_id, $code_type)
    {
        $stmt = $this->conn->prepare("
            UPDATE email_verification_codes 
            SET is_used = TRUE 
            WHERE user_id = ? AND code_type = ? AND is_used = FALSE
        ");
        $stmt->execute([$user_id, $code_type]);
    }

    private function markCodeAsUsed($code_id)
    {
        $stmt = $this->conn->prepare("
            UPDATE email_verification_codes 
            SET is_used = TRUE, used_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$code_id]);
    }

    private function incrementAttempts($code_id)
    {
        $stmt = $this->conn->prepare("
            UPDATE email_verification_codes 
            SET attempts = attempts + 1 
            WHERE id = ?
        ");
        $stmt->execute([$code_id]);
    }

    private function sendCodeEmail($email, $code, $user_name, $code_type)
    {
        // Enhanced email template for mandatory 2FA
        $subject = 'Kode Login Wajib - Indonesian PDF Letter Generator';

        $message = "
        <html>
        <head>
            <title>Kode Login Wajib</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
                .code-box { background: white; border: 2px solid #4F46E5; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
                .code { font-size: 36px; font-weight: bold; color: #4F46E5; letter-spacing: 8px; }
                .warning { background: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; }
                .security { background: #DBEAFE; border-left: 4px solid #3B82F6; padding: 15px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>Indonesian PDF Letter Generator</h1>
                    <p>🔐 Kode Login Wajib</p>
                </div>
                <div class='content'>
                    <p>Halo <strong>$user_name</strong>,</p>
                    <p>Untuk melanjutkan login ke akun Anda, masukkan kode verifikasi berikut:</p>

                    <div class='code-box'>
                        <div class='code'>$code</div>
                    </div>

                    <p style='text-align: center;'><strong>⏰ Kode berlaku selama 5 menit</strong></p>

                    <div class='security'>
                        <strong>🛡️ Keamanan Tingkat Tinggi:</strong><br>
                        • Kode verifikasi diperlukan untuk setiap login<br>
                        • Sistem ini melindungi akun Anda dari akses tidak sah<br>
                        • Tidak ada opsi \"ingat perangkat\" untuk keamanan maksimal
                    </div>

                    <div class='warning'>
                        <strong>⚠️ Peringatan Keamanan:</strong><br>
                        • JANGAN bagikan kode ini kepada siapa pun<br>
                        • Jika Anda tidak melakukan login, segera hubungi kami<br>
                        • Laporkan aktivitas mencurigakan ke admin
                    </div>

                    <p><strong>📋 Detail Login:</strong></p>
                    <ul>
                        <li><strong>Waktu:</strong> " . date('d M Y H:i:s') . " WIB</li>
                        <li><strong>IP Address:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "</li>
                        <li><strong>Browser:</strong> " . substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 50) . "...</li>
                    </ul>

                    <p style='margin-top: 30px; font-size: 12px; color: #666;'>
                        Email ini dikirim otomatis oleh sistem keamanan. Mohon jangan membalas email ini.
                    </p>
                </div>
            </div>
        </body>
        </html>";

        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Indonesian PDF Letter Generator Security <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion(),
            'X-Priority: 1',
            'Importance: High'
        ];

        // Try to send email, fallback to file-based email for development
        $mail_sent = false;

        // First try normal mail() function
        if (function_exists('mail')) {
            $mail_sent = mail($email, $subject, $message, implode("\r\n", $headers));
        }

        // If mail() fails or doesn't exist, use file-based email
        if (!$mail_sent) {
            // Include file-based email system
            $file_mail_path = __DIR__ . '/../includes/file_mail.php';
            if (file_exists($file_mail_path)) {
                require_once $file_mail_path;
                $mail_sent = sendFileEmail($email, $subject, $message, implode("\r\n", $headers));

                // Log that we're using file-based email
                error_log("2FA Code sent via file-based email. Code: $code, Email: $email");
            }
        }

        return $mail_sent;
    }

    private function logLoginAttempt($email, $attempt_type, $success, $failure_reason = null)
    {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO login_attempts 
                (email, ip_address, user_agent, attempt_type, success, failure_reason) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $email,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                $attempt_type,
                $success ? 1 : 0,
                $failure_reason
            ]);
        } catch (Exception $e) {
            error_log("Login attempt logging error: " . $e->getMessage());
        }
    }
}
