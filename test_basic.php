<?php
// Basic test without any redirects
echo "<!DOCTYPE html>
<html>
<head>
    <title>Basic Test</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100 p-8'>
    <div class='max-w-2xl mx-auto bg-white p-6 rounded-lg shadow'>
        <h1 class='text-2xl font-bold mb-4'>Basic System Test</h1>";

// Test 1: Check if files can be included
echo "<h2 class='text-lg font-semibold mb-2'>Test 1: File Includes</h2>";
try {
    require_once 'config/database.php';
    echo "<p class='text-green-600'>✅ config/database.php - OK</p>";
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ config/database.php - Error: " . $e->getMessage() . "</p>";
}

try {
    require_once 'classes/User.php';
    echo "<p class='text-green-600'>✅ classes/User.php - OK</p>";
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ classes/User.php - Error: " . $e->getMessage() . "</p>";
}

// Test 2: Session status
echo "<h2 class='text-lg font-semibold mb-2 mt-4'>Test 2: Session Status</h2>";
echo "<p>Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// Test 3: User class methods
echo "<h2 class='text-lg font-semibold mb-2 mt-4'>Test 3: User Class</h2>";
if (class_exists('User')) {
    echo "<p class='text-green-600'>✅ User class exists</p>";
    
    try {
        $is_logged_in = User::isLoggedIn();
        echo "<p>User::isLoggedIn(): " . ($is_logged_in ? 'TRUE' : 'FALSE') . "</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'>❌ User::isLoggedIn() error: " . $e->getMessage() . "</p>";
    }
    
    try {
        $current_user = User::getCurrentUser();
        echo "<p>User::getCurrentUser(): " . ($current_user ? 'User object' : 'NULL') . "</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'>❌ User::getCurrentUser() error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='text-red-600'>❌ User class not found</p>";
}

// Test 4: Database connection
echo "<h2 class='text-lg font-semibold mb-2 mt-4'>Test 4: Database Connection</h2>";
try {
    $database = new Database();
    $conn = $database->getConnection();
    echo "<p class='text-green-600'>✅ Database connection successful</p>";
    
    // Test query
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>Users in database: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 5: Session data
echo "<h2 class='text-lg font-semibold mb-2 mt-4'>Test 5: Session Data</h2>";
if (!empty($_SESSION)) {
    echo "<pre class='bg-gray-100 p-4 rounded text-sm'>" . print_r($_SESSION, true) . "</pre>";
} else {
    echo "<p>No session data</p>";
}

// Test links
echo "<h2 class='text-lg font-semibold mb-2 mt-4'>Test Links</h2>";
echo "<div class='space-x-4'>
        <a href='debug.php' class='bg-blue-600 text-white px-4 py-2 rounded'>Debug Page</a>
        <a href='login.php' class='bg-green-600 text-white px-4 py-2 rounded'>Login Page</a>
        <a href='index.php' class='bg-purple-600 text-white px-4 py-2 rounded'>Index Page</a>
      </div>";

echo "    </div>
</body>
</html>";
?>
