<?php
session_start();

// Simple debug
echo "Session status: " . session_status() . "<br>";
echo "Session ID: " . session_id() . "<br>";
echo "Session data: " . print_r($_SESSION, true) . "<br>";

// Check if logged_in is set
if (isset($_SESSION['logged_in'])) {
    echo "logged_in is set: " . ($_SESSION['logged_in'] ? 'true' : 'false') . "<br>";
} else {
    echo "logged_in is NOT set<br>";
}

// Simple login check without class
$is_logged_in = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
echo "Is logged in: " . ($is_logged_in ? 'YES' : 'NO') . "<br>";

if ($is_logged_in) {
    echo "User is logged in, should redirect to dashboard<br>";
    echo "<a href='dashboard'>Go to Dashboard</a><br>";
} else {
    echo "User is NOT logged in, showing login form<br>";
}

// Simple login form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simple_login'])) {
    $_SESSION['logged_in'] = true;
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'test';
    echo "Login successful! Session updated.<br>";
    echo "<a href='dashboard'>Go to Dashboard</a><br>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Login Test</title>
</head>
<body>
    <h1>Simple Login Test</h1>
    
    <form method="POST">
        <button type="submit" name="simple_login">Simple Login</button>
    </form>
    
    <br>
    <a href="?clear=1">Clear Session</a>
    
    <?php
    if (isset($_GET['clear'])) {
        session_destroy();
        echo "<br>Session cleared. <a href='simple_login'>Refresh</a>";
    }
    ?>
</body>
</html>
