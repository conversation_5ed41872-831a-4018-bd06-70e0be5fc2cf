<?php
require_once 'includes/file_mail.php';

// Handle actions
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'delete' && isset($_GET['file'])) {
        $file = basename($_GET['file']); // Security: only filename
        $filepath = __DIR__ . "/emails/" . $file;
        if (file_exists($filepath)) {
            unlink($filepath);
            header('Location: view-emails');
            exit;
        }
    } elseif ($_GET['action'] === 'clear_all') {
        $email_files = getEmailFiles();
        foreach ($email_files as $file) {
            unlink($file);
        }
        header('Location: view-emails');
        exit;
    }
}

$email_files = getEmailFiles();
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Development Emails - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-envelope-open mr-3 text-blue-600"></i>
                    Development Emails
                </h1>
                <div class="space-x-2">
                    <?php if (!empty($email_files)): ?>
                        <a href="?action=clear_all" 
                           onclick="return confirm('Hapus semua email?')"
                           class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            <i class="fas fa-trash mr-2"></i>Clear All
                        </a>
                    <?php endif; ?>
                    <a href="test-2fa-flow" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-vial mr-2"></i>Test 2FA
                    </a>
                </div>
            </div>

            <?php if (empty($email_files)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-600 mb-2">No Emails Found</h3>
                    <p class="text-gray-500 mb-6">Belum ada email yang tersimpan. Coba test 2FA flow untuk generate email.</p>
                    <a href="test-2fa-flow" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-play mr-2"></i>Test 2FA Flow
                    </a>
                </div>
            <?php else: ?>
                <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                    <p class="text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong><?php echo count($email_files); ?> email(s)</strong> tersimpan dalam development mode.
                        Klik email untuk melihat detail dan extract kode 2FA.
                    </p>
                </div>

                <div class="grid gap-4">
                    <?php foreach ($email_files as $index => $file): ?>
                        <?php
                        $filename = basename($file);
                        $timestamp = filemtime($file);
                        $size = filesize($file);
                        $code = extractCodeFromEmailFile($file);
                        
                        // Extract email details from filename
                        preg_match('/email_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})_/', $filename, $matches);
                        $date_str = isset($matches[1]) ? str_replace('_', ' ', $matches[1]) : date('Y-m-d H:i:s', $timestamp);
                        ?>
                        <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-envelope text-blue-600 mr-2"></i>
                                        <h3 class="font-medium text-gray-800">Email #<?php echo $index + 1; ?></h3>
                                        <?php if ($code): ?>
                                            <span class="ml-3 bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono">
                                                <i class="fas fa-key mr-1"></i><?php echo $code; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="text-sm text-gray-600 space-y-1">
                                        <p><i class="fas fa-clock mr-2"></i><?php echo $date_str; ?></p>
                                        <p><i class="fas fa-file mr-2"></i><?php echo $filename; ?></p>
                                        <p><i class="fas fa-weight mr-2"></i><?php echo number_format($size / 1024, 1); ?> KB</p>
                                    </div>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <button onclick="viewEmail('<?php echo $filename; ?>')" 
                                            class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <?php if ($code): ?>
                                        <button onclick="copyCode('<?php echo $code; ?>')" 
                                                class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                            <i class="fas fa-copy mr-1"></i>Copy Code
                                        </button>
                                    <?php endif; ?>
                                    <a href="?action=delete&file=<?php echo urlencode($filename); ?>" 
                                       onclick="return confirm('Hapus email ini?')"
                                       class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="mt-8 pt-6 border-t text-center space-x-4">
                <a href="login" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                    <i class="fas fa-sign-in-alt mr-2"></i>Test Login
                </a>
                <a href="debug-email" class="bg-purple-600 text-white px-6 py-2 rounded hover:bg-purple-700">
                    <i class="fas fa-bug mr-2"></i>Debug Email
                </a>
                <a href="setup-mail" class="bg-orange-600 text-white px-6 py-2 rounded hover:bg-orange-700">
                    <i class="fas fa-cog mr-2"></i>Setup Mail
                </a>
                <a href="index" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </div>

    <!-- Email Modal -->
    <div id="emailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-hidden">
                <div class="flex justify-between items-center p-4 border-b">
                    <h3 class="text-lg font-medium">Email Content</h3>
                    <button onclick="closeEmailModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="emailContent" class="p-4 overflow-y-auto max-h-96">
                    <!-- Email content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
    function viewEmail(filename) {
        fetch('emails/' + filename)
            .then(response => response.text())
            .then(html => {
                document.getElementById('emailContent').innerHTML = html;
                document.getElementById('emailModal').classList.remove('hidden');
            })
            .catch(error => {
                alert('Error loading email: ' + error);
            });
    }

    function closeEmailModal() {
        document.getElementById('emailModal').classList.add('hidden');
    }

    function copyCode(code) {
        navigator.clipboard.writeText(code).then(function() {
            // Show temporary success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-green-800');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-800');
                button.classList.add('bg-green-600', 'hover:bg-green-700');
            }, 2000);
        }).catch(function(err) {
            alert('Failed to copy code: ' + err);
        });
    }

    // Close modal when clicking outside
    document.getElementById('emailModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeEmailModal();
        }
    });

    // Auto-refresh every 30 seconds to check for new emails
    setInterval(function() {
        if (!document.getElementById('emailModal').classList.contains('hidden')) {
            return; // Don't refresh if modal is open
        }
        location.reload();
    }, 30000);
    </script>
</body>
</html>
