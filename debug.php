<?php
require_once 'config/database.php';
require_once 'classes/User.php';

// Clear session if requested
if (isset($_GET['clear'])) {
    session_destroy();
    session_start();
    header('Location: debug.php');
    exit;
}

// Simple login test
if (isset($_POST['simple_login'])) {
    $_SESSION['logged_in'] = true;
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'user';
    $_SESSION['full_name'] = 'Test User';
    header('Location: debug.php');
    exit;
}

// Simple logout test
if (isset($_POST['simple_logout'])) {
    unset($_SESSION['logged_in']);
    unset($_SESSION['user_id']);
    unset($_SESSION['username']);
    unset($_SESSION['email']);
    unset($_SESSION['role']);
    unset($_SESSION['full_name']);
    header('Location: debug.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Session - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Debug Session</h1>

        <!-- Session Status -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Session Status</h2>
            <div class="space-y-2 text-sm">
                <p><strong>Session Status:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?></p>
                <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                <p><strong>Session Data Count:</strong> <?php echo count($_SESSION); ?></p>
            </div>
            
            <?php if (!empty($_SESSION)): ?>
                <h3 class="font-medium mt-4 mb-2">Session Data:</h3>
                <div class="bg-gray-100 p-4 rounded text-xs font-mono">
                    <pre><?php print_r($_SESSION); ?></pre>
                </div>
            <?php else: ?>
                <p class="text-gray-600 mt-4">No session data</p>
            <?php endif; ?>
        </div>

        <!-- User Status -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">User Status</h2>
            <?php
            $is_logged_in = User::isLoggedIn();
            $current_user = User::getCurrentUser();
            ?>
            <div class="space-y-2 text-sm">
                <p><strong>User::isLoggedIn():</strong> 
                    <span class="<?php echo $is_logged_in ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo $is_logged_in ? 'TRUE' : 'FALSE'; ?>
                    </span>
                </p>
                
                <?php if ($current_user): ?>
                    <p><strong>Current User:</strong></p>
                    <div class="ml-4 space-y-1">
                        <p>ID: <?php echo $current_user['id'] ?? 'N/A'; ?></p>
                        <p>Username: <?php echo $current_user['username'] ?? 'N/A'; ?></p>
                        <p>Email: <?php echo $current_user['email'] ?? 'N/A'; ?></p>
                        <p>Role: <?php echo $current_user['role'] ?? 'N/A'; ?></p>
                    </div>
                <?php else: ?>
                    <p><strong>Current User:</strong> <span class="text-red-600">NULL</span></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Test Actions -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Actions</h2>
            <div class="space-x-4">
                <form method="POST" class="inline">
                    <button type="submit" name="simple_login" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        Simple Login
                    </button>
                </form>
                
                <form method="POST" class="inline">
                    <button type="submit" name="simple_logout" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        Simple Logout
                    </button>
                </form>
                
                <a href="?clear=1" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                    Clear Session
                </a>
            </div>
        </div>

        <!-- Test Redirects -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Redirects</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="index.php" class="bg-blue-600 text-white px-4 py-2 rounded text-center hover:bg-blue-700">
                    index.php
                </a>
                <a href="login.php" class="bg-green-600 text-white px-4 py-2 rounded text-center hover:bg-green-700">
                    login.php
                </a>
                <a href="dashboard.php" class="bg-purple-600 text-white px-4 py-2 rounded text-center hover:bg-purple-700">
                    dashboard.php
                </a>
                <a href="admin.php" class="bg-orange-600 text-white px-4 py-2 rounded text-center hover:bg-orange-700">
                    admin.php
                </a>
            </div>
        </div>

        <!-- Expected Behavior -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Expected Behavior</h2>
            <div class="text-sm space-y-2">
                <p><strong>If NOT logged in:</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li>index.php → should redirect to login.php</li>
                    <li>login.php → should show login form</li>
                    <li>dashboard.php → should redirect to login.php</li>
                </ul>
                
                <p class="mt-4"><strong>If logged in:</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li>index.php → should redirect to dashboard.php</li>
                    <li>login.php → should redirect to dashboard.php</li>
                    <li>dashboard.php → should show dashboard</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
