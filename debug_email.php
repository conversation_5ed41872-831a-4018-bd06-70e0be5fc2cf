<?php
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug Email System</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100 p-8'>
    <div class='max-w-4xl mx-auto'>
        <h1 class='text-2xl font-bold mb-6'>Debug Email System</h1>";

// Test email sending
if (isset($_POST['send_test_email'])) {
    $test_email = $_POST['test_email'] ?? '';
    $test_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    
    if (!empty($test_email)) {
        echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>
                Testing email to: " . htmlspecialchars($test_email) . "
              </div>";
        
        // Test 1: Simple mail() function
        echo "<h3 class='font-semibold mb-2'>Test 1: Simple mail() function</h3>";
        
        $subject = "Test Email - Simple";
        $message = "This is a simple test email. Code: $test_code";
        $headers = "From: test@localhost";
        
        if (mail($test_email, $subject, $message, $headers)) {
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
                    ✅ Simple mail() function: SUCCESS
                  </div>";
        } else {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                    ❌ Simple mail() function: FAILED
                  </div>";
        }
        
        // Test 2: HTML email
        echo "<h3 class='font-semibold mb-2 mt-4'>Test 2: HTML email</h3>";
        
        $subject = "Test Email - HTML";
        $message = "
        <html>
        <head><title>Test HTML Email</title></head>
        <body>
            <h2>Test HTML Email</h2>
            <p>This is a test HTML email.</p>
            <div style='background: #f0f0f0; padding: 20px; text-align: center;'>
                <h3 style='color: #4F46E5; font-size: 24px;'>$test_code</h3>
            </div>
        </body>
        </html>";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: test@localhost'
        ];
        
        if (mail($test_email, $subject, $message, implode("\r\n", $headers))) {
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
                    ✅ HTML email: SUCCESS
                  </div>";
        } else {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                    ❌ HTML email: FAILED
                  </div>";
        }
        
        // Test 3: Using TwoFactorAuth class
        echo "<h3 class='font-semibold mb-2 mt-4'>Test 3: TwoFactorAuth class</h3>";
        
        try {
            require_once 'classes/TwoFactorAuth.php';
            $twoFA = new TwoFactorAuth();
            
            // Create a test user scenario
            $result = $twoFA->generateAndSendCode(1, $test_email, 'test');
            
            if ($result['success']) {
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
                        ✅ TwoFactorAuth class: SUCCESS<br>
                        Message: " . htmlspecialchars($result['message']) . "
                      </div>";
            } else {
                echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                        ❌ TwoFactorAuth class: FAILED<br>
                        Error: " . htmlspecialchars($result['message']) . "
                      </div>";
            }
        } catch (Exception $e) {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>
                    ❌ TwoFactorAuth class: EXCEPTION<br>
                    Error: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
        }
    }
}

// PHP Mail Configuration Check
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>PHP Mail Configuration</h2>
        <div class='space-y-2 text-sm'>";

$mail_configs = [
    'mail() function' => function_exists('mail') ? 'Available' : 'NOT Available',
    'sendmail_path' => ini_get('sendmail_path') ?: 'Not set',
    'SMTP' => ini_get('SMTP') ?: 'Not set',
    'smtp_port' => ini_get('smtp_port') ?: 'Not set',
    'sendmail_from' => ini_get('sendmail_from') ?: 'Not set'
];

foreach ($mail_configs as $key => $value) {
    $color = ($value === 'NOT Available' || $value === 'Not set') ? 'text-red-600' : 'text-green-600';
    echo "<p><strong>$key:</strong> <span class='$color'>$value</span></p>";
}

echo "        </div>
      </div>";

// Error Log Check
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Recent Error Logs</h2>";

$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    $logs = file($error_log_file);
    $recent_logs = array_slice($logs, -10); // Last 10 lines
    
    echo "<div class='bg-gray-100 p-4 rounded text-xs font-mono max-h-64 overflow-y-auto'>";
    foreach ($recent_logs as $log) {
        echo htmlspecialchars($log) . "<br>";
    }
    echo "</div>";
} else {
    echo "<p class='text-gray-600'>Error log file not found or not accessible.</p>";
}

echo "</div>";

// Test Form
echo "<div class='bg-white rounded-lg shadow p-6'>
        <h2 class='text-lg font-semibold mb-4'>Test Email Sending</h2>
        <form method='POST' class='space-y-4'>
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-1'>Test Email Address:</label>
                <input type='email' name='test_email' required 
                       class='w-full px-3 py-2 border border-gray-300 rounded-md'
                       placeholder='<EMAIL>'>
            </div>
            <button type='submit' name='send_test_email' 
                    class='bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700'>
                Send Test Emails
            </button>
        </form>
      </div>";

// Alternative Email Solutions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6'>
        <h2 class='text-lg font-semibold text-yellow-800 mb-4'>Alternative Solutions</h2>
        <div class='text-sm text-yellow-700 space-y-2'>
            <p><strong>If mail() doesn't work:</strong></p>
            <ul class='list-disc list-inside ml-4 space-y-1'>
                <li>Install and configure a local mail server (like Mercury or hMailServer)</li>
                <li>Use SMTP with PHPMailer library</li>
                <li>Use a mail service like Gmail SMTP, SendGrid, or Mailgun</li>
                <li>For development: Use a mail catcher like MailHog or Mailtrap</li>
            </ul>
        </div>
      </div>";

echo "    </div>
</body>
</html>";
?>
