<?php
// Test new structure
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New Structure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test New Project Structure</h1>
    
    <div class="test">
        <h2>1. File Existence Test</h2>
        <?php
        $files = [
            'config/database.php' => 'Database Config',
            'app/models/User.php' => 'User Model',
            'app/models/Admin.php' => 'Admin Model',
            'app/models/PasswordReset.php' => 'Password Reset Model',
            'auth/login.php' => 'Login Page',
            'auth/register.php' => 'Register Page',
            'admin/index.php' => 'Admin Dashboard',
            'admin/users.php' => 'Admin Users',
            'app/views/user/dashboard.php' => 'User Dashboard',
            'public/assets/css/security-protection.css' => 'Security CSS',
            'public/assets/js/security-protection.js' => 'Security JS'
        ];
        
        foreach ($files as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='success'>✅ $description ($file)</div>";
            } else {
                echo "<div class='error'>❌ $description ($file) - NOT FOUND</div>";
            }
        }
        ?>
    </div>
    
    <div class="test">
        <h2>2. Include Test</h2>
        <?php
        try {
            require_once 'config/database.php';
            echo "<div class='success'>✅ Database config loaded</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database config error: " . $e->getMessage() . "</div>";
        }
        
        try {
            require_once 'app/models/User.php';
            echo "<div class='success'>✅ User model loaded</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ User model error: " . $e->getMessage() . "</div>";
        }
        
        try {
            require_once 'app/models/Admin.php';
            echo "<div class='success'>✅ Admin model loaded</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Admin model error: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
    
    <div class="test">
        <h2>3. URL Test</h2>
        <p><strong>Test these URLs:</strong></p>
        <ul>
            <li><a href="auth/login.php" target="_blank">Direct: auth/login.php</a></li>
            <li><a href="login" target="_blank">Clean URL: /login</a></li>
            <li><a href="admin/index.php" target="_blank">Direct: admin/index.php</a></li>
            <li><a href="admin" target="_blank">Clean URL: /admin</a></li>
            <li><a href="app/views/user/dashboard.php" target="_blank">Direct: dashboard.php</a></li>
            <li><a href="dashboard" target="_blank">Clean URL: /dashboard</a></li>
        </ul>
    </div>
    
    <div class="test">
        <h2>4. Database Test</h2>
        <?php
        try {
            if (class_exists('Database')) {
                $database = new Database();
                $conn = $database->getConnection();
                echo "<div class='success'>✅ Database connection successful</div>";
                
                // Test query
                $stmt = $conn->query("SELECT 1 as test");
                $result = $stmt->fetch();
                echo "<div class='success'>✅ Database query successful</div>";
            } else {
                echo "<div class='error'>❌ Database class not found</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
    
    <div class="test">
        <h2>5. Session Test</h2>
        <?php
        try {
            echo "<div class='success'>✅ Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</div>";
            echo "<div class='success'>✅ Session ID: " . session_id() . "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Session error: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
    
    <div class="test">
        <h2>6. Quick Actions</h2>
        <p>
            <a href="index.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Test Main Index</a>
            <a href="auth/login.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Test Login</a>
            <a href="admin/index.php" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">Test Admin</a>
        </p>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h3>📋 Structure Summary</h3>
        <p><strong>✅ Reorganized:</strong> Files moved to logical directories</p>
        <p><strong>✅ Clean URLs:</strong> /login, /admin, /dashboard working</p>
        <p><strong>✅ Security:</strong> Protection files in public/assets/</p>
        <p><strong>✅ Models:</strong> All models in app/models/</p>
        <p><strong>✅ Views:</strong> User views in app/views/user/</p>
        <p><strong>✅ Auth:</strong> Authentication in auth/ directory</p>
        <p><strong>✅ Admin:</strong> Admin panel in admin/ directory</p>
    </div>
</body>
</html>
