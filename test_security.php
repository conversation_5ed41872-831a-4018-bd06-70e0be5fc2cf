<?php
require_once 'config/database.php';
require_once 'classes/User.php';

// Only allow admin access
if (!User::isLoggedIn() || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/security-protection.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-shield-alt mr-3 text-red-600"></i>
                Security Protection Test
            </h1>

            <!-- Security Status -->
            <div class="mb-8 p-6 bg-red-50 border border-red-200 rounded-lg">
                <h2 class="text-lg font-semibold text-red-800 mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Security Status
                </h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Right-click disabled</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">View source disabled</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Developer tools detection</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Text selection disabled</span>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Print protection enabled</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Image saving disabled</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Console warnings active</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span class="text-sm">Anti-debugging enabled</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Instructions -->
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Manual Tests -->
                <div class="bg-yellow-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                        <i class="fas fa-hand-pointer mr-2"></i>Manual Tests
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="border-l-4 border-yellow-400 pl-4">
                            <h3 class="font-medium text-yellow-800">Right-Click Test</h3>
                            <p class="text-sm text-yellow-700">Try right-clicking anywhere on this page. You should see a security notification.</p>
                        </div>
                        
                        <div class="border-l-4 border-yellow-400 pl-4">
                            <h3 class="font-medium text-yellow-800">Keyboard Shortcuts Test</h3>
                            <p class="text-sm text-yellow-700">Try pressing F12, Ctrl+U, Ctrl+Shift+I, or Ctrl+S. All should be blocked.</p>
                        </div>
                        
                        <div class="border-l-4 border-yellow-400 pl-4">
                            <h3 class="font-medium text-yellow-800">Text Selection Test</h3>
                            <p class="text-sm text-yellow-700 no-inspect">Try selecting this text. It should not be selectable.</p>
                        </div>
                        
                        <div class="border-l-4 border-yellow-400 pl-4">
                            <h3 class="font-medium text-yellow-800">Image Drag Test</h3>
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjNEY0NkU1Ii8+Cjx0ZXh0IHg9IjIwIiB5PSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VEVTVDwvdGV4dD4KPHN2Zz4K" alt="Test Image" class="inline-block mr-2">
                            <p class="text-sm text-yellow-700">Try dragging this image. It should not be draggable.</p>
                        </div>
                    </div>
                </div>

                <!-- Automated Tests -->
                <div class="bg-green-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-green-800 mb-4">
                        <i class="fas fa-robot mr-2"></i>Automated Tests
                    </h2>
                    
                    <div class="space-y-4">
                        <button onclick="testConsoleAccess()" class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            <i class="fas fa-terminal mr-2"></i>Test Console Access
                        </button>
                        
                        <button onclick="testDevToolsDetection()" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            <i class="fas fa-search mr-2"></i>Test DevTools Detection
                        </button>
                        
                        <button onclick="testSecurityHeaders()" class="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            <i class="fas fa-shield-alt mr-2"></i>Test Security Headers
                        </button>
                        
                        <button onclick="testSourceAccess()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            <i class="fas fa-code mr-2"></i>Test Source Access
                        </button>
                    </div>
                    
                    <div id="testResults" class="mt-4 space-y-2"></div>
                </div>
            </div>

            <!-- Security Logs -->
            <div class="mt-8 bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-list mr-2"></i>Security Event Log
                </h2>
                <div id="securityLog" class="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                    <div class="text-green-300">[SECURITY] Protection system initialized</div>
                    <div class="text-yellow-300">[WARNING] This page is for testing security features only</div>
                    <div class="text-blue-300">[INFO] All protection layers are active</div>
                </div>
            </div>

            <!-- Protection Details -->
            <div class="mt-8 bg-blue-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>Protection Details
                </h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-blue-800 mb-2">JavaScript Protection</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Right-click context menu disabled</li>
                            <li>• Keyboard shortcuts blocked (F12, Ctrl+U, etc.)</li>
                            <li>• Developer tools detection</li>
                            <li>• Anti-debugging techniques</li>
                            <li>• Console clearing and warnings</li>
                            <li>• Text selection prevention</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium text-blue-800 mb-2">CSS Protection</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• User selection disabled</li>
                            <li>• Image dragging prevented</li>
                            <li>• Print protection</li>
                            <li>• Mobile touch protection</li>
                            <li>• Scrollbar customization</li>
                            <li>• Content watermarking</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-8 text-center space-x-4">
                <a href="login.php" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-eye mr-2"></i>View Protected Login
                </a>
                <a href="admin.php" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Admin
                </a>
            </div>
        </div>
    </div>

    <script>
        // Test functions
        function testConsoleAccess() {
            logSecurityEvent('Testing console access...');
            try {
                console.log('Console access test');
                logSecurityEvent('[PASS] Console accessible (expected for admin)', 'success');
            } catch (e) {
                logSecurityEvent('[FAIL] Console blocked: ' + e.message, 'error');
            }
        }

        function testDevToolsDetection() {
            logSecurityEvent('Testing developer tools detection...');
            const threshold = 160;
            const isDevToolsOpen = window.outerHeight - window.innerHeight > threshold || 
                                   window.outerWidth - window.innerWidth > threshold;
            
            if (isDevToolsOpen) {
                logSecurityEvent('[DETECTED] Developer tools appear to be open', 'warning');
            } else {
                logSecurityEvent('[PASS] No developer tools detected', 'success');
            }
        }

        function testSecurityHeaders() {
            logSecurityEvent('Testing security headers...');
            fetch(window.location.href, { method: 'HEAD' })
                .then(response => {
                    const headers = ['x-frame-options', 'x-content-type-options', 'x-xss-protection'];
                    headers.forEach(header => {
                        if (response.headers.get(header)) {
                            logSecurityEvent(`[PASS] ${header} header present`, 'success');
                        } else {
                            logSecurityEvent(`[FAIL] ${header} header missing`, 'error');
                        }
                    });
                })
                .catch(e => {
                    logSecurityEvent('[ERROR] Failed to test headers: ' + e.message, 'error');
                });
        }

        function testSourceAccess() {
            logSecurityEvent('Testing source code access...');
            fetch(window.location.href + '?view-source')
                .then(response => {
                    if (response.status === 403) {
                        logSecurityEvent('[PASS] Source access blocked', 'success');
                    } else {
                        logSecurityEvent('[WARNING] Source access may be possible', 'warning');
                    }
                })
                .catch(e => {
                    logSecurityEvent('[PASS] Source access blocked by network', 'success');
                });
        }

        function logSecurityEvent(message, type = 'info') {
            const log = document.getElementById('securityLog');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': 'text-blue-300',
                'success': 'text-green-300',
                'warning': 'text-yellow-300',
                'error': 'text-red-300'
            };
            
            const entry = document.createElement('div');
            entry.className = colors[type] || 'text-white';
            entry.textContent = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Monitor security events
        document.addEventListener('contextmenu', () => {
            logSecurityEvent('Right-click attempt detected', 'warning');
        });

        document.addEventListener('keydown', (e) => {
            if (e.keyCode === 123 || (e.ctrlKey && e.keyCode === 85)) {
                logSecurityEvent('Blocked keyboard shortcut: ' + e.key, 'warning');
            }
        });

        // Initialize
        logSecurityEvent('Security test page loaded');
        logSecurityEvent('Monitoring for security events...');
    </script>

    <!-- Security Protection Script -->
    <script src="assets/js/security-protection.js"></script>
</body>
</html>
