<?php
// Debug Error Page - Indonesian PDF Letter Generator
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug Error - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100 p-8'>
    <div class='max-w-6xl mx-auto'>
        <h1 class='text-2xl font-bold mb-6 text-red-600'>🔍 Debug Internal Server Error</h1>";

// Test 1: Basic PHP functionality
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 1: Basic PHP</h2>";

try {
    echo "<p class='text-green-600'>✅ PHP berjalan normal</p>";
    echo "<p>PHP Version: " . phpversion() . "</p>";
    echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ PHP Error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 2: File includes
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 2: File Includes</h2>";

// Test config/database.php
echo "<h3 class='font-medium mb-2'>Testing config/database.php:</h3>";
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "<p class='text-green-600'>✅ config/database.php loaded successfully</p>";
    } else {
        echo "<p class='text-red-600'>❌ config/database.php not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ Error loading config/database.php: " . $e->getMessage() . "</p>";
}

// Test classes/User.php
echo "<h3 class='font-medium mb-2 mt-4'>Testing classes/User.php:</h3>";
try {
    if (file_exists('classes/User.php')) {
        require_once 'classes/User.php';
        echo "<p class='text-green-600'>✅ classes/User.php loaded successfully</p>";
    } else {
        echo "<p class='text-red-600'>❌ classes/User.php not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ Error loading classes/User.php: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 3: Database connection
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 3: Database Connection</h2>";

try {
    if (class_exists('Database')) {
        $database = new Database();
        $conn = $database->getConnection();
        echo "<p class='text-green-600'>✅ Database connection successful</p>";
        
        // Test query
        $stmt = $conn->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "<p class='text-green-600'>✅ Database query successful</p>";
    } else {
        echo "<p class='text-red-600'>❌ Database class not available</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 4: Session
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 4: Session</h2>";

try {
    echo "<p>Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p class='text-green-600'>✅ Session working</p>";
} catch (Exception $e) {
    echo "<p class='text-red-600'>❌ Session error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 5: Error logs
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 5: Error Logs</h2>";

$error_log_locations = [
    'C:/xampp/apache/logs/error.log',
    'C:/xampp/php/logs/php_error_log',
    ini_get('error_log'),
    'error.log',
    '../logs/error.log'
];

foreach ($error_log_locations as $log_file) {
    if ($log_file && file_exists($log_file)) {
        echo "<h3 class='font-medium mb-2'>Error Log: $log_file</h3>";
        $logs = file($log_file);
        $recent_logs = array_slice($logs, -20); // Last 20 lines
        
        echo "<div class='bg-gray-100 p-4 rounded text-xs font-mono max-h-64 overflow-y-auto'>";
        foreach ($recent_logs as $log) {
            $log = trim($log);
            if (!empty($log)) {
                if (strpos($log, 'error') !== false || strpos($log, 'Error') !== false) {
                    echo "<div class='text-red-600'>" . htmlspecialchars($log) . "</div>";
                } elseif (strpos($log, 'warning') !== false || strpos($log, 'Warning') !== false) {
                    echo "<div class='text-yellow-600'>" . htmlspecialchars($log) . "</div>";
                } else {
                    echo "<div>" . htmlspecialchars($log) . "</div>";
                }
            }
        }
        echo "</div>";
        break;
    }
}

if (!isset($log_file) || !file_exists($log_file)) {
    echo "<p class='text-yellow-600'>⚠️ Error log file not found. Check these locations:</p>";
    echo "<ul class='list-disc list-inside text-sm'>";
    foreach ($error_log_locations as $location) {
        echo "<li>" . htmlspecialchars($location) . "</li>";
    }
    echo "</ul>";
}

echo "</div>";

// Test 6: .htaccess
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 6: .htaccess</h2>";

if (file_exists('.htaccess')) {
    echo "<p class='text-green-600'>✅ .htaccess file exists</p>";
    
    $htaccess_content = file_get_contents('.htaccess');
    $lines = explode("\n", $htaccess_content);
    $problematic_lines = [];
    
    foreach ($lines as $line_num => $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) continue;
        
        // Check for common problematic directives
        if (strpos($line, 'RewriteEngine') !== false ||
            strpos($line, 'RewriteRule') !== false ||
            strpos($line, 'Header') !== false ||
            strpos($line, 'FilesMatch') !== false) {
            // These are potentially problematic
            $problematic_lines[] = ($line_num + 1) . ": " . $line;
        }
    }
    
    if (!empty($problematic_lines)) {
        echo "<p class='text-yellow-600'>⚠️ Potentially problematic .htaccess lines:</p>";
        echo "<div class='bg-gray-100 p-4 rounded text-xs font-mono max-h-32 overflow-y-auto'>";
        foreach (array_slice($problematic_lines, 0, 10) as $line) {
            echo htmlspecialchars($line) . "<br>";
        }
        echo "</div>";
    }
} else {
    echo "<p class='text-red-600'>❌ .htaccess file not found</p>";
}

echo "</div>";

// Test 7: File permissions
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test 7: File Permissions</h2>";

$files_to_check = [
    'index.php',
    'login.php',
    'config/database.php',
    'classes/User.php',
    '.htaccess'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? 'Yes' : 'No';
        echo "<p>$file - Readable: $readable, Permissions: " . decoct($perms & 0777) . "</p>";
    } else {
        echo "<p class='text-red-600'>$file - File not found</p>";
    }
}

echo "</div>";

// Quick fixes
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>
        <h2 class='text-lg font-semibold text-yellow-800 mb-4'>🔧 Quick Fixes</h2>
        <div class='space-y-2 text-sm'>
            <p><strong>1. Backup dan rename .htaccess:</strong></p>
            <p class='ml-4 font-mono bg-gray-100 p-2 rounded'>mv .htaccess .htaccess.backup</p>
            
            <p><strong>2. Check PHP error log di XAMPP:</strong></p>
            <p class='ml-4'>C:\\xampp\\apache\\logs\\error.log</p>
            
            <p><strong>3. Restart Apache di XAMPP Control Panel</strong></p>
            
            <p><strong>4. Test tanpa .htaccess:</strong></p>
            <p class='ml-4'>Akses: http://localhost/surat/login.php (dengan .php)</p>
        </div>
      </div>";

echo "    </div>
</body>
</html>";
?>
