# Test Data untuk Generator Surat PDF

## Data Test untuk Surat Pernyataan
- **Nama**: <PERSON>
- **Tempat/Tanggal Lahir**: Jakarta, 15 Agustus 1990
- **Alamat**: Jl. Merdeka No. 123, RT.05/RW.02, <PERSON><PERSON><PERSON><PERSON>, Kecamatan Menteng, Jakarta Pusat 10310
- **<PERSON><PERSON>**: <PERSON><PERSON> men<PERSON> bahwa saya tidak pernah terlibat dalam kegiatan yang melanggar hukum dan akan mematuhi semua peraturan yang berlaku di perusahaan. Saya juga berkomitmen untuk bekerja dengan jujur dan bertanggung jawab.
- **Tanggal**: 6 Juli 2025

## Data Test untuk Surat Izin
- **Nama**: Siti Nurhaliza
- **Kelas**: XII IPA 2
- **Ke<PERSON>luan Izin**: tidak dapat mengikuti kegiatan pembelajaran pada hari <PERSON>, 7 Juli 2025 dikarenakan harus mengurus administrasi pendaftaran universitas di Jakarta
- **Tanggal**: 6 Juli 2025

## Data Test untuk Surat Kuasa
- **Nama <PERSON>asa**: <PERSON><PERSON>, S.H., M.H.
- **Nama Penerima Kuasa**: Andi Pratama, S.H.
- **Keperluan**: mengurus dan menandatangani dokumen-dokumen yang berkaitan dengan proses jual beli tanah dan bangunan yang terletak di Jl. Sudirman No. 45, Jakarta Selatan, termasuk namun tidak terbatas pada penandatanganan akta jual beli, pembayaran pajak, dan pengurusan sertifikat
- **Tanggal**: 6 Juli 2025

## Hasil yang Diharapkan
1. **Alignment yang konsisten**: Semua label (Nama:, Alamat:, dll.) harus sejajar vertikal
2. **Spacing yang seragam**: Jarak antar baris dan paragraf konsisten
3. **Text wrapping yang rapi**: Teks panjang (alamat, isi pernyataan) ter-wrap dengan indentasi yang benar
4. **Margin yang konsisten**: Margin kiri dan kanan seragam di seluruh dokumen
5. **Format profesional**: Tampilan yang rapi dan mudah dibaca
