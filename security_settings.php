<?php
session_start();
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/TwoFactorAuth.php';

// Require login
User::requireLogin();

$current_user = User::getCurrentUser();
$twoFA = new TwoFactorAuth();
$success_message = '';
$error_message = '';

// Get user's 2FA status
try {
    $database = new Database();
    $conn = $database->getConnection();
    $stmt = $conn->prepare("SELECT two_factor_enabled FROM users WHERE id = ?");
    $stmt->execute([$current_user['id']]);
    $user_security = $stmt->fetch();
    $is_2fa_enabled = $user_security ? (bool)$user_security['two_factor_enabled'] : false;
} catch (Exception $e) {
    error_log("Error getting 2FA status: " . $e->getMessage());
    $is_2fa_enabled = false;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['enable_2fa'])) {
        // Enable 2FA
        if ($twoFA->enable2FA($current_user['id'])) {
            $success_message = '2FA berhasil diaktifkan! Anda akan diminta kode verifikasi saat login berikutnya.';
            $is_2fa_enabled = true;
        } else {
            $error_message = 'Gagal mengaktifkan 2FA. Silakan coba lagi.';
        }
    } elseif (isset($_POST['disable_2fa'])) {
        // Disable 2FA
        if ($twoFA->disable2FA($current_user['id'])) {
            $success_message = '2FA berhasil dinonaktifkan.';
            $is_2fa_enabled = false;
        } else {
            $error_message = 'Gagal menonaktifkan 2FA. Silakan coba lagi.';
        }
    } elseif (isset($_POST['test_2fa'])) {
        // Test 2FA by sending code
        $result = $twoFA->generateAndSendCode($current_user['id'], $current_user['email'], 'login');
        if ($result['success']) {
            $success_message = 'Kode test 2FA telah dikirim ke email Anda: ' . $current_user['email'];
        } else {
            $error_message = 'Gagal mengirim kode test: ' . $result['message'];
        }
    }
}

// Get trusted devices
try {
    $stmt = $conn->prepare("
        SELECT device_name, ip_address, created_at, last_used_at, expires_at 
        FROM trusted_devices 
        WHERE user_id = ? AND is_active = TRUE AND expires_at > NOW()
        ORDER BY last_used_at DESC
    ");
    $stmt->execute([$current_user['id']]);
    $trusted_devices = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting trusted devices: " . $e->getMessage());
    $trusted_devices = [];
}

// Get recent login attempts
try {
    $stmt = $conn->prepare("
        SELECT ip_address, user_agent, attempt_type, success, failure_reason, created_at 
        FROM login_attempts 
        WHERE email = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$current_user['email']]);
    $login_attempts = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting login attempts: " . $e->getMessage());
    $login_attempts = [];
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan Keamanan - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto py-8 px-4">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">
                        <i class="fas fa-shield-alt mr-3 text-blue-600"></i>
                        Pengaturan Keamanan
                    </h1>
                    <p class="text-gray-600 mt-2">Kelola keamanan akun dan autentikasi dua faktor</p>
                </div>
                <a href="profile" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Kembali ke Profile
                </a>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-3"></i>
                    <span><?php echo htmlspecialchars($success_message); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-3"></i>
                    <span><?php echo htmlspecialchars($error_message); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid lg:grid-cols-2 gap-8">
            <!-- 2FA Settings -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-mobile-alt mr-2 text-green-600"></i>
                    Autentikasi Dua Faktor (2FA)
                </h2>
                
                <div class="mb-6">
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <h3 class="font-medium text-gray-800">Status 2FA</h3>
                            <p class="text-sm text-gray-600">
                                <?php if ($is_2fa_enabled): ?>
                                    2FA aktif - Akun Anda dilindungi dengan verifikasi email
                                <?php else: ?>
                                    2FA tidak aktif - Aktifkan untuk keamanan tambahan
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="flex items-center">
                            <?php if ($is_2fa_enabled): ?>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-check mr-1"></i>Aktif
                                </span>
                            <?php else: ?>
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-times mr-1"></i>Tidak Aktif
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <?php if ($is_2fa_enabled): ?>
                        <form method="POST" class="space-y-4">
                            <button type="submit" name="test_2fa" 
                                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-paper-plane mr-2"></i>Test 2FA - Kirim Kode ke Email
                            </button>
                            <button type="submit" name="disable_2fa" 
                                    onclick="return confirm('Yakin ingin menonaktifkan 2FA? Ini akan mengurangi keamanan akun Anda.')"
                                    class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-times mr-2"></i>Nonaktifkan 2FA
                            </button>
                        </form>
                    <?php else: ?>
                        <form method="POST">
                            <button type="submit" name="enable_2fa" 
                                    class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-shield-alt mr-2"></i>Aktifkan 2FA
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-medium text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>Tentang 2FA
                    </h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Kode verifikasi dikirim ke email Anda saat login</li>
                        <li>• Melindungi akun meski password bocor</li>
                        <li>• Perangkat dapat dipercaya selama 30 hari</li>
                        <li>• Kode berlaku selama 5 menit</li>
                    </ul>
                </div>
            </div>

            <!-- Trusted Devices -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-laptop mr-2 text-purple-600"></i>
                    Perangkat Terpercaya
                </h2>
                
                <?php if (empty($trusted_devices)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-laptop text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-500">Belum ada perangkat terpercaya</p>
                        <p class="text-sm text-gray-400 mt-2">
                            Perangkat akan muncul di sini setelah Anda memilih "Percayai perangkat ini" saat login 2FA
                        </p>
                    </div>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php foreach ($trusted_devices as $device): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="font-medium text-gray-800">
                                            <?php echo htmlspecialchars($device['device_name'] ?? 'Perangkat Tidak Dikenal'); ?>
                                        </h4>
                                        <p class="text-sm text-gray-600">IP: <?php echo htmlspecialchars($device['ip_address']); ?></p>
                                        <p class="text-xs text-gray-500">
                                            Terakhir digunakan: <?php echo date('d M Y H:i', strtotime($device['last_used_at'])); ?>
                                        </p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-xs text-gray-500">
                                            Berakhir: <?php echo date('d M Y', strtotime($device['expires_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Login Attempts -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">
                <i class="fas fa-history mr-2 text-orange-600"></i>
                Riwayat Login Terakhir
            </h2>
            
            <?php if (empty($login_attempts)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-history text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500">Belum ada riwayat login</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4">Waktu</th>
                                <th class="text-left py-3 px-4">IP Address</th>
                                <th class="text-left py-3 px-4">Jenis</th>
                                <th class="text-left py-3 px-4">Status</th>
                                <th class="text-left py-3 px-4">Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($login_attempts as $attempt): ?>
                                <tr class="border-b border-gray-100">
                                    <td class="py-3 px-4">
                                        <?php echo date('d M Y H:i', strtotime($attempt['created_at'])); ?>
                                    </td>
                                    <td class="py-3 px-4 font-mono text-xs">
                                        <?php echo htmlspecialchars($attempt['ip_address']); ?>
                                    </td>
                                    <td class="py-3 px-4">
                                        <?php
                                        $type_labels = [
                                            'password' => 'Password',
                                            '2fa_code' => '2FA Code',
                                            'admin_login' => 'Admin Login'
                                        ];
                                        echo $type_labels[$attempt['attempt_type']] ?? $attempt['attempt_type'];
                                        ?>
                                    </td>
                                    <td class="py-3 px-4">
                                        <?php if ($attempt['success']): ?>
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                                                <i class="fas fa-check mr-1"></i>Berhasil
                                            </span>
                                        <?php else: ?>
                                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                                                <i class="fas fa-times mr-1"></i>Gagal
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3 px-4 text-xs text-gray-600">
                                        <?php echo htmlspecialchars($attempt['failure_reason'] ?? '-'); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
