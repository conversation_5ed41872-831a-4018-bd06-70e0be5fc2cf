<?php
// Quick debug for 500 error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug 500 Error</h1>";

// Test 1: Basic PHP
echo "<h2>1. PHP Test</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current directory: " . getcwd() . "<br>";

// Test 2: File existence
echo "<h2>2. File Existence Test</h2>";
$files = [
    'index.php',
    'config/database.php',
    'app/models/User.php',
    'auth/login.php',
    'admin/index.php',
    '.htaccess'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file - EXISTS<br>";
    } else {
        echo "❌ $file - NOT FOUND<br>";
    }
}

// Test 3: Include test
echo "<h2>3. Include Test</h2>";
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "✅ config/database.php loaded<br>";
    }
} catch (Exception $e) {
    echo "❌ Error loading config/database.php: " . $e->getMessage() . "<br>";
}

try {
    if (file_exists('app/models/User.php')) {
        require_once 'app/models/User.php';
        echo "✅ app/models/User.php loaded<br>";
    }
} catch (Exception $e) {
    echo "❌ Error loading app/models/User.php: " . $e->getMessage() . "<br>";
}

// Test 4: .htaccess syntax
echo "<h2>4. .htaccess Test</h2>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess exists<br>";
    $htaccess = file_get_contents('.htaccess');
    $lines = explode("\n", $htaccess);
    $error_lines = [];
    
    foreach ($lines as $num => $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) continue;
        
        // Check for common syntax errors
        if (strpos($line, 'RewriteRule') !== false && substr_count($line, ' ') < 2) {
            $error_lines[] = ($num + 1) . ": $line";
        }
        if (strpos($line, '<') !== false && strpos($line, '>') === false) {
            $error_lines[] = ($num + 1) . ": $line (unclosed tag)";
        }
    }
    
    if (empty($error_lines)) {
        echo "✅ No obvious .htaccess syntax errors<br>";
    } else {
        echo "⚠️ Potential .htaccess issues:<br>";
        foreach ($error_lines as $error) {
            echo "- $error<br>";
        }
    }
} else {
    echo "❌ .htaccess not found<br>";
}

echo "<h2>5. Quick Links</h2>";
echo "<a href='auth/login.php'>Direct: auth/login.php</a><br>";
echo "<a href='admin/index.php'>Direct: admin/index.php</a><br>";
echo "<a href='app/views/user/dashboard.php'>Direct: app/views/user/dashboard.php</a><br>";
?>
