<?php
require_once 'config/database.php';
require_once 'classes/TwoFactorAuth.php';

$test_result = '';
$latest_code = '';

if (isset($_POST['test_email_system'])) {
    $test_email = $_POST['test_email'] ?? '<EMAIL>';
    
    try {
        $twoFA = new TwoFactorAuth();
        
        // Generate and send test code
        $result = $twoFA->generateAndSendCode(1, $test_email, 'test');
        
        if ($result['success']) {
            $test_result = "✅ SUCCESS: Email system working! Code sent to $test_email";
            
            // Try to get the latest code from saved emails
            require_once 'includes/file_mail.php';
            $email_files = getEmailFiles();
            if (!empty($email_files)) {
                $latest_file = $email_files[0]; // Most recent
                $latest_code = extractCodeFromEmailFile($latest_file);
            }
        } else {
            $test_result = "❌ FAILED: " . $result['message'];
        }
    } catch (Exception $e) {
        $test_result = "❌ ERROR: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Email Test - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-2xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-bolt mr-3 text-yellow-600"></i>
                Quick Email Test
            </h1>

            <!-- Test Result -->
            <?php if ($test_result): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo strpos($test_result, '✅') !== false ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?>">
                    <p class="font-medium"><?php echo htmlspecialchars($test_result); ?></p>
                    <?php if ($latest_code): ?>
                        <div class="mt-3 p-3 bg-white rounded border">
                            <p class="text-sm text-gray-600">Latest 2FA Code:</p>
                            <p class="text-2xl font-mono font-bold text-blue-600"><?php echo $latest_code; ?></p>
                            <button onclick="copyToClipboard('<?php echo $latest_code; ?>')" 
                                    class="mt-2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                <i class="fas fa-copy mr-1"></i>Copy Code
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Test Form -->
            <form method="POST" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Test Email Address:</label>
                    <input type="email" name="test_email" value="<EMAIL>" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Email akan disimpan ke file, tidak dikirim ke email asli</p>
                </div>
                
                <button type="submit" name="test_email_system" 
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-paper-plane mr-2"></i>Test Email System
                </button>
            </form>

            <!-- System Status -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-medium text-gray-800 mb-3">System Status</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>PHP mail() function:</span>
                        <span class="<?php echo function_exists('mail') ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo function_exists('mail') ? 'Available' : 'Not Available'; ?>
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>File-based email:</span>
                        <span class="<?php echo file_exists('includes/file_mail.php') ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo file_exists('includes/file_mail.php') ? 'Available' : 'Not Available'; ?>
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>TwoFactorAuth class:</span>
                        <span class="<?php echo class_exists('TwoFactorAuth') ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo class_exists('TwoFactorAuth') ? 'Available' : 'Not Available'; ?>
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>Emails directory:</span>
                        <span class="<?php echo is_dir('emails') ? 'text-green-600' : 'text-yellow-600'; ?>">
                            <?php echo is_dir('emails') ? 'Exists' : 'Will be created'; ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 grid grid-cols-2 gap-4">
                <a href="view-emails" class="bg-green-600 text-white px-4 py-2 rounded text-center hover:bg-green-700">
                    <i class="fas fa-envelope-open mr-2"></i>View Emails
                </a>
                <a href="test-2fa-flow" class="bg-purple-600 text-white px-4 py-2 rounded text-center hover:bg-purple-700">
                    <i class="fas fa-vial mr-2"></i>Test 2FA Flow
                </a>
                <a href="debug-email" class="bg-orange-600 text-white px-4 py-2 rounded text-center hover:bg-orange-700">
                    <i class="fas fa-bug mr-2"></i>Debug Email
                </a>
                <a href="setup-mail" class="bg-blue-600 text-white px-4 py-2 rounded text-center hover:bg-blue-700">
                    <i class="fas fa-cog mr-2"></i>Setup Mail
                </a>
            </div>

            <!-- Instructions -->
            <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-medium text-blue-800 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>How It Works
                </h3>
                <div class="text-sm text-blue-700 space-y-1">
                    <p>1. Sistem akan mencoba mengirim email menggunakan PHP mail() function</p>
                    <p>2. Jika gagal, akan fallback ke file-based email system</p>
                    <p>3. Email disimpan ke folder 'emails/' sebagai file HTML</p>
                    <p>4. Kode 2FA dapat di-extract dari file email</p>
                    <p>5. Gunakan kode tersebut untuk test verifikasi 2FA</p>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-8 text-center">
                <a href="index" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Home
                </a>
            </div>
        </div>
    </div>

    <script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success feedback
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-blue-600', 'hover:bg-blue-700');
            }, 2000);
        }).catch(function(err) {
            alert('Failed to copy: ' + err);
        });
    }
    </script>
</body>
</html>
