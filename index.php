<?php
// Main entry point for Indonesian PDF Letter Generator
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test basic functionality first
try {
    require_once 'config/database.php';
    echo "<!-- Database config loaded -->\n";
} catch (Exception $e) {
    die("Error loading database config: " . $e->getMessage());
}

try {
    require_once 'app/models/User.php';
    echo "<!-- User model loaded -->\n";
} catch (Exception $e) {
    die("Error loading User model: " . $e->getMessage());
}

// Prevent redirect loops
if (!isset($_SESSION['redirect_count'])) {
    $_SESSION['redirect_count'] = 0;
}

$_SESSION['redirect_count']++;

if ($_SESSION['redirect_count'] > 3) {
    // Too many redirects, show error
    unset($_SESSION['redirect_count']);
    die('Redirect loop detected. Please clear your browser cache and cookies, then try again.');
}

// Check if user is logged in
try {
    if (User::isLoggedIn()) {
        // Reset redirect count on successful access
        unset($_SESSION['redirect_count']);
        // Redirect to dashboard
        header('Location: app/views/user/dashboard.php');
        exit;
    } else {
        // Redirect to login
        header('Location: auth/login.php');
        exit;
    }
} catch (Exception $e) {
    die("Error checking user login status: " . $e->getMessage());
}
