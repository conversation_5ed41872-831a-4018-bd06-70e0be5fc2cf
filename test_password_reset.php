<?php
require_once 'config/database.php';
require_once 'classes/PasswordReset.php';

$test_results = [];

// Handle test actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['test_forgot_password'])) {
        $test_email = $_POST['test_email'] ?? '';
        
        if (!empty($test_email)) {
            $passwordReset = new PasswordReset();
            $result = $passwordReset->sendResetToken($test_email);
            
            $test_results['forgot_password'] = [
                'status' => $result['success'] ? 'success' : 'error',
                'message' => $result['message'],
                'email' => $test_email
            ];
        }
    }
    
    if (isset($_POST['test_reset_password'])) {
        $test_token = $_POST['test_token'] ?? '';
        $test_new_password = $_POST['test_new_password'] ?? '';
        
        if (!empty($test_token) && !empty($test_new_password)) {
            $passwordReset = new PasswordReset();
            $result = $passwordReset->resetPassword($test_token, $test_new_password);
            
            $test_results['reset_password'] = [
                'status' => $result['success'] ? 'success' : 'error',
                'message' => $result['message']
            ];
        }
    }
    
    if (isset($_POST['cleanup_tokens'])) {
        $passwordReset = new PasswordReset();
        $cleaned = $passwordReset->cleanupExpiredTokens();
        
        $test_results['cleanup'] = [
            'status' => 'success',
            'message' => "Cleaned up $cleaned expired tokens"
        ];
    }
}

// Get recent emails
$recent_emails = [];
if (file_exists('includes/file_mail.php')) {
    require_once 'includes/file_mail.php';
    $email_files = getEmailFiles();
    $recent_emails = array_slice($email_files, 0, 5); // Last 5 emails
}

// Get recent reset tokens
$recent_tokens = [];
try {
    $database = new Database();
    $conn = $database->getConnection();
    $stmt = $conn->query("
        SELECT prt.*, u.username, u.email 
        FROM password_reset_tokens prt 
        JOIN users u ON prt.user_id = u.id 
        ORDER BY prt.created_at DESC 
        LIMIT 5
    ");
    $recent_tokens = $stmt->fetchAll();
} catch (Exception $e) {
    // Ignore if table doesn't exist yet
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Password Reset - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-vial mr-3 text-purple-600"></i>
                Test Password Reset System
            </h1>

            <!-- Test Results -->
            <?php if (!empty($test_results)): ?>
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Test Results</h2>
                    <div class="space-y-3">
                        <?php foreach ($test_results as $test_name => $result): ?>
                            <div class="border rounded-lg p-4 <?php 
                                echo $result['status'] === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; 
                            ?>">
                                <div class="flex items-center">
                                    <i class="fas <?php 
                                        echo $result['status'] === 'success' ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'; 
                                    ?> mr-3"></i>
                                    <div>
                                        <h3 class="font-medium capitalize"><?php echo str_replace('_', ' ', $test_name); ?></h3>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($result['message']); ?></p>
                                        <?php if (isset($result['email'])): ?>
                                            <p class="text-xs text-gray-500">Email: <?php echo htmlspecialchars($result['email']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Test Forgot Password -->
                <div class="bg-yellow-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                        <i class="fas fa-key mr-2"></i>Test Forgot Password
                    </h2>
                    
                    <form method="POST" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Test Email:</label>
                            <input type="email" name="test_email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-yellow-500"
                                   placeholder="<EMAIL>">
                        </div>
                        <button type="submit" name="test_forgot_password" 
                                class="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>Send Reset Token
                        </button>
                    </form>
                    
                    <div class="mt-4 text-sm text-yellow-700">
                        <p><strong>Expected:</strong> Reset token akan digenerate dan email dikirim (atau disimpan ke file untuk development)</p>
                    </div>
                </div>

                <!-- Test Reset Password -->
                <div class="bg-green-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-green-800 mb-4">
                        <i class="fas fa-lock mr-2"></i>Test Reset Password
                    </h2>
                    
                    <form method="POST" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Reset Token:</label>
                            <input type="text" name="test_token" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500"
                                   placeholder="Token dari email atau database">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">New Password:</label>
                            <input type="password" name="test_new_password" required minlength="6"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500"
                                   placeholder="Password baru (min 6 karakter)">
                        </div>
                        <button type="submit" name="test_reset_password" 
                                class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>Reset Password
                        </button>
                    </form>
                    
                    <div class="mt-4 text-sm text-green-700">
                        <p><strong>Expected:</strong> Token akan diverifikasi dan password user akan diupdate jika valid</p>
                    </div>
                </div>
            </div>

            <!-- Recent Emails -->
            <?php if (!empty($recent_emails)): ?>
                <div class="mt-8 bg-blue-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-blue-800 mb-4">
                        <i class="fas fa-envelope mr-2"></i>Recent Password Reset Emails
                    </h2>
                    <div class="space-y-3">
                        <?php foreach ($recent_emails as $email_file): ?>
                            <?php
                            $filename = basename($email_file);
                            $timestamp = filemtime($email_file);
                            $reset_link = extractResetLinkFromEmailFile($email_file);
                            ?>
                            <div class="bg-white rounded border p-3">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium text-gray-800"><?php echo $filename; ?></p>
                                        <p class="text-sm text-gray-600"><?php echo date('d M Y H:i:s', $timestamp); ?></p>
                                        <?php if ($reset_link): ?>
                                            <p class="text-xs text-blue-600 mt-1">Reset link available</p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="space-x-2">
                                        <a href="<?php echo $email_file; ?>" target="_blank" 
                                           class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                            View
                                        </a>
                                        <?php if ($reset_link): ?>
                                            <a href="<?php echo $reset_link; ?>" target="_blank" 
                                               class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                                Test Link
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Tokens -->
            <?php if (!empty($recent_tokens)): ?>
                <div class="mt-8 bg-purple-50 rounded-lg p-6">
                    <h2 class="text-lg font-semibold text-purple-800 mb-4">
                        <i class="fas fa-database mr-2"></i>Recent Reset Tokens
                    </h2>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b">
                                    <th class="text-left p-2">User</th>
                                    <th class="text-left p-2">Token</th>
                                    <th class="text-left p-2">Expires</th>
                                    <th class="text-left p-2">Used</th>
                                    <th class="text-left p-2">Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_tokens as $token): ?>
                                    <tr class="border-b">
                                        <td class="p-2">
                                            <div class="font-medium"><?php echo htmlspecialchars($token['username']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($token['email']); ?></div>
                                        </td>
                                        <td class="p-2">
                                            <code class="bg-gray-100 px-2 py-1 rounded text-xs">
                                                <?php echo substr($token['token'], 0, 16) . '...'; ?>
                                            </code>
                                        </td>
                                        <td class="p-2"><?php echo date('d M H:i', strtotime($token['expires_at'])); ?></td>
                                        <td class="p-2">
                                            <span class="<?php echo $token['used'] ? 'text-red-600' : 'text-green-600'; ?>">
                                                <?php echo $token['used'] ? 'Yes' : 'No'; ?>
                                            </span>
                                        </td>
                                        <td class="p-2"><?php echo date('d M H:i', strtotime($token['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Cleanup Tools -->
            <div class="mt-8 bg-red-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-red-800 mb-4">
                    <i class="fas fa-broom mr-2"></i>Cleanup Tools
                </h2>
                <form method="POST" class="inline">
                    <button type="submit" name="cleanup_tokens" 
                            onclick="return confirm('Clean up expired tokens?')"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-trash mr-2"></i>Cleanup Expired Tokens
                    </button>
                </form>
            </div>

            <!-- Quick Links -->
            <div class="mt-8 text-center space-x-4">
                <a href="forgot-password" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-key mr-2"></i>Forgot Password
                </a>
                <a href="setup-password-reset" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-cog mr-2"></i>Setup Database
                </a>
                <a href="login" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>Login
                </a>
                <a href="index" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>
