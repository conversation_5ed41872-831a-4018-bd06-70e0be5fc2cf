<?php
require_once 'config/database.php';
require_once 'classes/User.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Redirect - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
</head>
<body class='bg-gray-100 p-8'>
    <div class='max-w-2xl mx-auto'>
        <h1 class='text-2xl font-bold mb-6'>Test Redirect System</h1>";

// Session status
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Session Status</h2>
        <div class='space-y-2'>
            <p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>
            <p><strong>Session ID:</strong> " . session_id() . "</p>
            <p><strong>Session Data Count:</strong> " . count($_SESSION) . "</p>";

if (!empty($_SESSION)) {
    echo "<details class='mt-4'>
            <summary class='cursor-pointer text-blue-600 hover:text-blue-800'>Show Session Data</summary>
            <pre class='mt-2 p-4 bg-gray-100 rounded text-xs'>" . print_r($_SESSION, true) . "</pre>
          </details>";
}

echo "        </div>
      </div>";

// User status
$is_logged_in = User::isLoggedIn();
$current_user = User::getCurrentUser();

echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>User Status</h2>
        <div class='space-y-2'>
            <p><strong>Is Logged In:</strong> <span class='" . ($is_logged_in ? 'text-green-600' : 'text-red-600') . "'>" . ($is_logged_in ? 'Yes' : 'No') . "</span></p>";

if ($current_user) {
    echo "<p><strong>User ID:</strong> {$current_user['id']}</p>
          <p><strong>Username:</strong> {$current_user['username']}</p>
          <p><strong>Email:</strong> {$current_user['email']}</p>
          <p><strong>Role:</strong> {$current_user['role']}</p>";
} else {
    echo "<p><strong>Current User:</strong> <span class='text-red-600'>None</span></p>";
}

echo "        </div>
      </div>";

// Test redirects
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test Redirects</h2>
        <div class='grid md:grid-cols-2 gap-4'>
            <div>
                <h3 class='font-medium mb-2'>Main Pages</h3>
                <div class='space-y-2'>
                    <a href='index' class='block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-center'>Index</a>
                    <a href='login' class='block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-center'>Login</a>
                    <a href='register' class='block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-center'>Register</a>
                    <a href='dashboard' class='block bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 text-center'>Dashboard</a>
                </div>
            </div>
            <div>
                <h3 class='font-medium mb-2'>Admin Pages</h3>
                <div class='space-y-2'>
                    <a href='admin' class='block bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 text-center'>Admin</a>
                    <a href='admin/login' class='block bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700 text-center'>Admin Login</a>
                    <a href='admin/users' class='block bg-pink-600 text-white px-4 py-2 rounded hover:bg-pink-700 text-center'>Admin Users</a>
                </div>
            </div>
        </div>
      </div>";

// Test actions
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>
        <h2 class='text-lg font-semibold mb-4'>Test Actions</h2>
        <div class='space-x-4'>
            <a href='?clear_session=1' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700'>Clear Session</a>
            <a href='?fake_login=1' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>Fake Login</a>
            <a href='?logout=1' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'>Logout</a>
        </div>
      </div>";

// Handle actions
if (isset($_GET['clear_session'])) {
    session_destroy();
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>
            Session cleared. <a href='test-redirect' class='underline'>Refresh</a>
          </div>";
}

if (isset($_GET['fake_login'])) {
    $_SESSION['logged_in'] = true;
    $_SESSION['user_id'] = 999;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'user';
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
            Fake login successful. <a href='test-redirect' class='underline'>Refresh</a>
          </div>";
}

if (isset($_GET['logout'])) {
    unset($_SESSION['logged_in']);
    unset($_SESSION['user_id']);
    unset($_SESSION['username']);
    unset($_SESSION['email']);
    unset($_SESSION['role']);
    echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>
            Logged out. <a href='test-redirect' class='underline'>Refresh</a>
          </div>";
}

// Redirect test results
echo "<div class='bg-white rounded-lg shadow p-6'>
        <h2 class='text-lg font-semibold mb-4'>Expected Behavior</h2>
        <div class='space-y-2 text-sm'>
            <p><strong>If NOT logged in:</strong></p>
            <ul class='list-disc list-inside ml-4 space-y-1'>
                <li>Index → should redirect to Login</li>
                <li>Login → should show login form</li>
                <li>Dashboard → should redirect to Login</li>
                <li>Admin → should redirect to Admin Login</li>
            </ul>
            <p class='mt-4'><strong>If logged in:</strong></p>
            <ul class='list-disc list-inside ml-4 space-y-1'>
                <li>Index → should redirect to Dashboard</li>
                <li>Login → should redirect to Dashboard</li>
                <li>Dashboard → should show dashboard</li>
                <li>Admin → should check admin role</li>
            </ul>
        </div>
      </div>";

echo "    </div>
</body>
</html>";
?>
