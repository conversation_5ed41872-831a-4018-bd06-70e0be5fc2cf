<?php
/**
 * Setup 2FA Database Schema
 * Indonesian PDF Letter Generator
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup 2FA - Indonesian PDF Letter Generator</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-gray-100 min-h-screen py-8'>
    <div class='max-w-4xl mx-auto px-4'>
        <div class='bg-white rounded-lg shadow-lg p-8'>
            <h1 class='text-2xl font-bold text-gray-800 mb-6'>
                <i class='fas fa-shield-alt mr-3 text-blue-600'></i>
                Setup Two-Factor Authentication (2FA)
            </h1>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<div class='space-y-4'>";
    
    // Read and execute SQL file
    $sql_file = 'database/2fa_schema_update.sql';
    
    if (!file_exists($sql_file)) {
        throw new Exception("SQL file not found: $sql_file");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL commands (simple split by semicolon)
    $sql_commands = array_filter(array_map('trim', explode(';', $sql_content)));
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($sql_commands as $sql) {
        if (empty($sql) || strpos($sql, '--') === 0) {
            continue; // Skip empty lines and comments
        }
        
        try {
            // Skip DELIMITER commands (MySQL specific)
            if (strpos($sql, 'DELIMITER') !== false) {
                continue;
            }
            
            // Skip CREATE EVENT commands for now (requires SUPER privilege)
            if (strpos($sql, 'CREATE EVENT') !== false) {
                echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>
                        <i class='fas fa-exclamation-triangle mr-2'></i>
                        Skipped: CREATE EVENT (requires SUPER privilege)
                      </div>";
                continue;
            }
            
            $conn->exec($sql);
            $success_count++;
            
            // Show success for important operations
            if (strpos($sql, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE\s+(\w+)/i', $sql, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                        <i class='fas fa-check mr-2'></i>
                        Created table: <strong>$table_name</strong>
                      </div>";
            } elseif (strpos($sql, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE\s+(\w+)/i', $sql, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded'>
                        <i class='fas fa-edit mr-2'></i>
                        Modified table: <strong>$table_name</strong>
                      </div>";
            } elseif (strpos($sql, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO\s+(\w+)/i', $sql, $matches);
                $table_name = $matches[1] ?? 'unknown';
                echo "<div class='bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded'>
                        <i class='fas fa-plus mr-2'></i>
                        Inserted data into: <strong>$table_name</strong>
                      </div>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            $error_msg = $e->getMessage();
            
            // Check if it's a "table already exists" error
            if (strpos($error_msg, 'already exists') !== false) {
                echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>
                        <i class='fas fa-info-circle mr-2'></i>
                        Table already exists (skipped)
                      </div>";
            } else {
                echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                        <i class='fas fa-times mr-2'></i>
                        Error: " . htmlspecialchars($error_msg) . "
                      </div>";
            }
        }
    }
    
    echo "</div>";
    
    // Summary
    echo "<div class='mt-8 p-6 bg-gray-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-gray-800 mb-4'>Setup Summary</h2>
            <div class='grid md:grid-cols-2 gap-4'>
                <div class='bg-green-100 p-4 rounded-lg'>
                    <div class='flex items-center'>
                        <i class='fas fa-check-circle text-green-600 text-2xl mr-3'></i>
                        <div>
                            <div class='font-semibold text-green-800'>Successful Operations</div>
                            <div class='text-green-600'>$success_count commands executed</div>
                        </div>
                    </div>
                </div>
                <div class='bg-red-100 p-4 rounded-lg'>
                    <div class='flex items-center'>
                        <i class='fas fa-exclamation-circle text-red-600 text-2xl mr-3'></i>
                        <div>
                            <div class='font-semibold text-red-800'>Errors</div>
                            <div class='text-red-600'>$error_count errors encountered</div>
                        </div>
                    </div>
                </div>
            </div>
          </div>";
    
    // Test 2FA functionality
    echo "<div class='mt-8 p-6 bg-blue-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-blue-800 mb-4'>
                <i class='fas fa-vial mr-2'></i>Test 2FA Setup
            </h2>";
    
    // Check if tables exist
    $tables_to_check = [
        'email_verification_codes',
        'login_attempts', 
        'trusted_devices',
        'email_templates'
    ];
    
    $all_tables_exist = true;
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='text-green-700 mb-2'>
                        <i class='fas fa-check mr-2'></i>Table <strong>$table</strong> exists
                      </div>";
            } else {
                echo "<div class='text-red-700 mb-2'>
                        <i class='fas fa-times mr-2'></i>Table <strong>$table</strong> missing
                      </div>";
                $all_tables_exist = false;
            }
        } catch (Exception $e) {
            echo "<div class='text-red-700 mb-2'>
                    <i class='fas fa-times mr-2'></i>Error checking table <strong>$table</strong>: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
            $all_tables_exist = false;
        }
    }
    
    // Check if users table has 2FA columns
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'two_factor_enabled'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='text-green-700 mb-2'>
                    <i class='fas fa-check mr-2'></i>Users table has 2FA columns
                  </div>";
        } else {
            echo "<div class='text-red-700 mb-2'>
                    <i class='fas fa-times mr-2'></i>Users table missing 2FA columns
                  </div>";
            $all_tables_exist = false;
        }
    } catch (Exception $e) {
        echo "<div class='text-red-700 mb-2'>
                <i class='fas fa-times mr-2'></i>Error checking users table: " . htmlspecialchars($e->getMessage()) . "
              </div>";
        $all_tables_exist = false;
    }
    
    if ($all_tables_exist) {
        echo "<div class='mt-4 p-4 bg-green-100 border border-green-400 rounded-lg'>
                <div class='flex items-center'>
                    <i class='fas fa-thumbs-up text-green-600 text-xl mr-3'></i>
                    <div>
                        <div class='font-semibold text-green-800'>2FA Setup Complete!</div>
                        <div class='text-green-700'>All required tables and columns are in place.</div>
                    </div>
                </div>
              </div>";
    } else {
        echo "<div class='mt-4 p-4 bg-red-100 border border-red-400 rounded-lg'>
                <div class='flex items-center'>
                    <i class='fas fa-exclamation-triangle text-red-600 text-xl mr-3'></i>
                    <div>
                        <div class='font-semibold text-red-800'>Setup Incomplete</div>
                        <div class='text-red-700'>Some tables or columns are missing. Please check the errors above.</div>
                    </div>
                </div>
              </div>";
    }
    
    echo "</div>";
    
    // Next steps
    echo "<div class='mt-8 p-6 bg-indigo-50 rounded-lg'>
            <h2 class='text-lg font-semibold text-indigo-800 mb-4'>
                <i class='fas fa-list-ol mr-2'></i>Next Steps
            </h2>
            <ol class='list-decimal list-inside space-y-2 text-indigo-700'>
                <li>Configure email settings in your environment or database</li>
                <li>Test email sending functionality</li>
                <li>Enable 2FA for your user account in Security Settings</li>
                <li>Test the complete 2FA login flow</li>
                <li>Consider enabling forced 2FA for admin users</li>
            </ol>
          </div>";
    
    // Quick links
    echo "<div class='mt-8 text-center space-x-4'>
            <a href='security-settings' class='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'>
                <i class='fas fa-shield-alt mr-2'></i>Security Settings
            </a>
            <a href='login' class='bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors'>
                <i class='fas fa-sign-in-alt mr-2'></i>Test Login
            </a>
            <a href='index' class='bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors'>
                <i class='fas fa-home mr-2'></i>Home
            </a>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
            <i class='fas fa-exclamation-triangle mr-2'></i>
            <strong>Setup Failed:</strong> " . htmlspecialchars($e->getMessage()) . "
          </div>";
    
    echo "<div class='mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded'>
            <h3 class='font-semibold text-yellow-800 mb-2'>Troubleshooting:</h3>
            <ul class='list-disc list-inside text-yellow-700 space-y-1'>
                <li>Make sure your database connection is working</li>
                <li>Check if the database user has CREATE and ALTER privileges</li>
                <li>Verify that the SQL file exists: database/2fa_schema_update.sql</li>
                <li>Check the error logs for more details</li>
            </ul>
          </div>";
}

echo "        </div>
    </div>
</body>
</html>";
?>
