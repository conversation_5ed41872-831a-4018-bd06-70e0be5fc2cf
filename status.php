<?php
// Status page untuk monitoring sistem
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-card { transition: all 0.3s ease; }
        .status-card:hover { transform: translateY(-2px); }
        .status-good { border-left: 4px solid #22c55e; }
        .status-warning { border-left: 4px solid #f59e0b; }
        .status-error { border-left: 4px solid #ef4444; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-6xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-heartbeat mr-3 text-green-600"></i>
                System Status Dashboard
            </h1>

            <!-- Quick Status Overview -->
            <div class="grid md:grid-cols-4 gap-4 mb-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <i class="fas fa-server text-2xl text-green-600 mb-2"></i>
                    <h3 class="font-semibold text-green-800">Server</h3>
                    <p class="text-green-600">Online</p>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                    <i class="fas fa-code text-2xl text-blue-600 mb-2"></i>
                    <h3 class="font-semibold text-blue-800">PHP</h3>
                    <p class="text-blue-600"><?php echo phpversion(); ?></p>
                </div>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                    <i class="fas fa-globe text-2xl text-purple-600 mb-2"></i>
                    <h3 class="font-semibold text-purple-800">Apache</h3>
                    <p class="text-purple-600">Running</p>
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                    <i class="fas fa-clock text-2xl text-yellow-600 mb-2"></i>
                    <h3 class="font-semibold text-yellow-800">Uptime</h3>
                    <p class="text-yellow-600"><?php echo date('H:i:s'); ?></p>
                </div>
            </div>

            <!-- Detailed Status -->
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- System Health -->
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-stethoscope mr-2"></i>System Health
                    </h2>
                    
                    <div class="space-y-3">
                        <!-- PHP Status -->
                        <div class="status-card bg-white border rounded-lg p-4 status-good">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <span class="font-medium">PHP Engine</span>
                                </div>
                                <span class="text-green-600 text-sm">Running</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">Version: <?php echo phpversion(); ?></p>
                        </div>

                        <!-- Session Status -->
                        <div class="status-card bg-white border rounded-lg p-4 status-good">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <span class="font-medium">Session System</span>
                                </div>
                                <span class="text-green-600 text-sm">Active</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">Status: <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?></p>
                        </div>

                        <!-- File System -->
                        <div class="status-card bg-white border rounded-lg p-4 <?php echo is_writable('.') ? 'status-good' : 'status-warning'; ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-<?php echo is_writable('.') ? 'check-circle text-green-600' : 'exclamation-triangle text-yellow-600'; ?> mr-3"></i>
                                    <span class="font-medium">File System</span>
                                </div>
                                <span class="<?php echo is_writable('.') ? 'text-green-600' : 'text-yellow-600'; ?> text-sm">
                                    <?php echo is_writable('.') ? 'Writable' : 'Read-only'; ?>
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">Directory permissions check</p>
                        </div>

                        <!-- Memory Usage -->
                        <div class="status-card bg-white border rounded-lg p-4 status-good">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-memory text-blue-600 mr-3"></i>
                                    <span class="font-medium">Memory Usage</span>
                                </div>
                                <span class="text-blue-600 text-sm"><?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">Peak: <?php echo round(memory_get_peak_usage() / 1024 / 1024, 2); ?> MB</p>
                        </div>
                    </div>
                </div>

                <!-- Application Status -->
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-cogs mr-2"></i>Application Status
                    </h2>
                    
                    <div class="space-y-3">
                        <!-- Core Files -->
                        <?php
                        $core_files = [
                            'index.php' => 'Main Entry Point',
                            'login.php' => 'Login System',
                            'config/database.php' => 'Database Config',
                            'classes/User.php' => 'User Management'
                        ];
                        
                        foreach ($core_files as $file => $description) {
                            $exists = file_exists($file);
                            $status_class = $exists ? 'status-good' : 'status-error';
                            $icon_class = $exists ? 'fas fa-check-circle text-green-600' : 'fas fa-times-circle text-red-600';
                            $status_text = $exists ? 'Available' : 'Missing';
                            $status_color = $exists ? 'text-green-600' : 'text-red-600';
                            
                            echo "<div class='status-card bg-white border rounded-lg p-4 $status_class'>
                                    <div class='flex items-center justify-between'>
                                        <div class='flex items-center'>
                                            <i class='$icon_class mr-3'></i>
                                            <span class='font-medium'>$description</span>
                                        </div>
                                        <span class='$status_color text-sm'>$status_text</span>
                                    </div>
                                    <p class='text-sm text-gray-600 mt-1'>$file</p>
                                  </div>";
                        }
                        ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-tools mr-2"></i>Quick Actions
                </h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="test_simple.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center">
                        <i class="fas fa-vial mr-2"></i>Simple Test
                    </a>
                    <a href="debug_error.php" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-center">
                        <i class="fas fa-bug mr-2"></i>Debug Errors
                    </a>
                    <a href="login_simple.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>Simple Login
                    </a>
                    <a href="login.php" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center">
                        <i class="fas fa-lock mr-2"></i>Full Login
                    </a>
                </div>
            </div>

            <!-- System Information -->
            <div class="mt-8 bg-blue-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>System Information
                </h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-blue-800 mb-2">Server Environment</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li><strong>OS:</strong> <?php echo PHP_OS; ?></li>
                            <li><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                            <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
                            <li><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-medium text-blue-800 mb-2">Application Info</h3>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                            <li><strong>Timezone:</strong> <?php echo date_default_timezone_get(); ?></li>
                            <li><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                            <li><strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Auto Refresh -->
            <div class="mt-6 text-center">
                <button onclick="location.reload()" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh Status
                </button>
            </div>
        </div>
    </div>

    <script>
        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Add timestamp to title
        document.title = 'System Status - ' + new Date().toLocaleTimeString();
    </script>
</body>
</html>
