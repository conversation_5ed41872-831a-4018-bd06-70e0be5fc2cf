/**
 * Home Page Styles
 * Indonesian PDF Letter Generator
 * Enhanced Color Scheme for Professional Indonesian Business Context
 */

/* CSS Variables for Indonesian Professional Theme */
:root {
  --primary-blue: #1e40af;
  --primary-navy: #1e3a8a;
  --indonesian-red: #dc2626;
  --golden-yellow: #f59e0b;
  --emerald-green: #059669;
  --slate-gray: #475569;
  --cool-gray: #6b7280;
  --light-gray: #f8fafc;
  --warm-orange: #ea580c;
  --success-green: #16a34a;
  --warning-amber: #d97706;
  --info-blue: #0284c7;
  --white: #ffffff;
  --black: #1f2937;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced gradient backgrounds with Indonesian theme */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-blue) 50%, var(--indonesian-red) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--golden-yellow) 0%, var(--warm-orange) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, var(--emerald-green) 0%, var(--success-green) 100%);
}

.gradient-indonesian {
  background: linear-gradient(135deg, var(--indonesian-red) 0%, var(--primary-navy) 100%);
}

/* Hero section enhancements */
.hero-text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-content h1,
.hero-content p {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.hero-content .stats-counter {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Feature cards hover effects */
.feature-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-icon {
  transition: transform 0.3s ease;
}

/* Template cards animations */
.template-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(111, 8, 245), transparent);
  transition: left 0.5s ease;
}

.template-card:hover::before {
  left: 100%;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Stats counter animations */
.stats-counter {
  font-family: "Arial", sans-serif;
  font-weight: 800;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating animation improvements */
.floating-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-20px) rotate(-1deg);
  }
}

/* Pulse animation for CTA buttons */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

/* Navigation enhancements */
.nav-link {
  position: relative;
  transition: color 0.3s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #4f46e5;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Mobile menu animations */
.mobile-menu {
  transform: translateY(-100%);
  transition: transform 0.3s ease;
}

.mobile-menu.show {
  transform: translateY(0);
}

/* Scroll reveal animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Responsive typography */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .gradient-bg {
    background: #667eea !important;
    color: white !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .feature-card {
    border: 2px solid #000;
  }

  .template-card {
    border: 2px solid #000;
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-animation,
  .pulse-animation {
    animation: none;
  }

  .feature-card,
  .template-card {
    transition: none;
  }

  .scroll-reveal {
    opacity: 1;
    transform: none;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
  }

  .text-gray-900 {
    color: #ffffff !important;
  }

  .text-gray-600 {
    color: #d1d5db !important;
  }

  .border-gray-100 {
    border-color: #374151 !important;
  }
}
