<?php
/**
 * Database Cleanup Script - Remove 2FA Tables
 * Indonesian PDF Letter Generator
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='id'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Database Cleanup - Remove 2FA Tables</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-gray-100 min-h-screen py-8'>
    <div class='max-w-4xl mx-auto px-4'>
        <div class='bg-white rounded-lg shadow-lg p-8'>
            <h1 class='text-2xl font-bold text-gray-800 mb-6'>
                <i class='fas fa-database mr-3 text-red-600'></i>
                Database Cleanup - Remove 2FA Tables
            </h1>";

if (isset($_POST['confirm_cleanup'])) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        echo "<div class='space-y-4'>";
        
        // List of 2FA-related tables to remove
        $tables_to_remove = [
            'email_verification_codes',
            'login_attempts', 
            'trusted_devices',
            'email_templates'
        ];
        
        $success_count = 0;
        $error_count = 0;
        
        foreach ($tables_to_remove as $table) {
            try {
                // Check if table exists first
                $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    // Table exists, drop it
                    $conn->exec("DROP TABLE IF EXISTS `$table`");
                    $success_count++;
                    
                    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                            <i class='fas fa-check mr-2'></i>
                            Dropped table: <strong>$table</strong>
                          </div>";
                } else {
                    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>
                            <i class='fas fa-info-circle mr-2'></i>
                            Table <strong>$table</strong> does not exist (already removed)
                          </div>";
                }
            } catch (PDOException $e) {
                $error_count++;
                echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                        <i class='fas fa-times mr-2'></i>
                        Error dropping table <strong>$table</strong>: " . htmlspecialchars($e->getMessage()) . "
                      </div>";
            }
        }
        
        // Remove 2FA columns from users table
        try {
            // Check if columns exist
            $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'two_factor_enabled'");
            if ($stmt->rowCount() > 0) {
                $conn->exec("ALTER TABLE users DROP COLUMN two_factor_enabled");
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                        <i class='fas fa-check mr-2'></i>
                        Removed column: <strong>users.two_factor_enabled</strong>
                      </div>";
            }
            
            $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'two_factor_secret'");
            if ($stmt->rowCount() > 0) {
                $conn->exec("ALTER TABLE users DROP COLUMN two_factor_secret");
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>
                        <i class='fas fa-check mr-2'></i>
                        Removed column: <strong>users.two_factor_secret</strong>
                      </div>";
            }
        } catch (PDOException $e) {
            $error_count++;
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                    <i class='fas fa-times mr-2'></i>
                    Error removing 2FA columns: " . htmlspecialchars($e->getMessage()) . "
                  </div>";
        }
        
        echo "</div>";
        
        // Summary
        echo "<div class='mt-8 p-6 bg-gray-50 rounded-lg'>
                <h2 class='text-lg font-semibold text-gray-800 mb-4'>Cleanup Summary</h2>
                <div class='grid md:grid-cols-2 gap-4'>
                    <div class='bg-green-100 p-4 rounded-lg'>
                        <div class='flex items-center'>
                            <i class='fas fa-check-circle text-green-600 text-2xl mr-3'></i>
                            <div>
                                <div class='font-semibold text-green-800'>Successful Operations</div>
                                <div class='text-green-600'>$success_count operations completed</div>
                            </div>
                        </div>
                    </div>
                    <div class='bg-red-100 p-4 rounded-lg'>
                        <div class='flex items-center'>
                            <i class='fas fa-exclamation-circle text-red-600 text-2xl mr-3'></i>
                            <div>
                                <div class='font-semibold text-red-800'>Errors</div>
                                <div class='text-red-600'>$error_count errors encountered</div>
                            </div>
                        </div>
                    </div>
                </div>
              </div>";
        
        if ($error_count == 0) {
            echo "<div class='mt-6 p-4 bg-green-100 border border-green-400 rounded-lg'>
                    <div class='flex items-center'>
                        <i class='fas fa-thumbs-up text-green-600 text-xl mr-3'></i>
                        <div>
                            <div class='font-semibold text-green-800'>Database Cleanup Complete!</div>
                            <div class='text-green-700'>All 2FA-related tables and columns have been removed.</div>
                        </div>
                    </div>
                  </div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
                <i class='fas fa-exclamation-triangle mr-2'></i>
                <strong>Cleanup Failed:</strong> " . htmlspecialchars($e->getMessage()) . "
              </div>";
    }
} else {
    // Show confirmation form
    echo "<div class='mb-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg'>
            <h2 class='text-lg font-semibold text-yellow-800 mb-4'>
                <i class='fas fa-exclamation-triangle mr-2'></i>Warning
            </h2>
            <p class='text-yellow-700 mb-4'>
                This will permanently remove all 2FA-related database tables and columns:
            </p>
            <ul class='list-disc list-inside text-yellow-700 space-y-1 mb-4'>
                <li><strong>email_verification_codes</strong> - 2FA verification codes</li>
                <li><strong>login_attempts</strong> - Login attempt logs</li>
                <li><strong>trusted_devices</strong> - Trusted device records</li>
                <li><strong>email_templates</strong> - Email templates</li>
                <li><strong>users.two_factor_enabled</strong> - 2FA enabled flag</li>
                <li><strong>users.two_factor_secret</strong> - 2FA secret keys</li>
            </ul>
            <p class='text-yellow-700 font-medium'>
                This action cannot be undone. Make sure you have a database backup if needed.
            </p>
          </div>";
    
    echo "<form method='POST' class='space-y-6'>
            <div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                <label class='flex items-center'>
                    <input type='checkbox' name='confirm_understanding' required 
                           class='mr-3 rounded border-red-300 text-red-600 focus:ring-red-500'>
                    <span class='text-red-700'>
                        I understand that this will permanently delete 2FA-related data and cannot be undone.
                    </span>
                </label>
            </div>
            
            <div class='flex space-x-4'>
                <button type='submit' name='confirm_cleanup' 
                        class='bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors'>
                    <i class='fas fa-trash mr-2'></i>Confirm Database Cleanup
                </button>
                <a href='index' class='bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors'>
                    <i class='fas fa-times mr-2'></i>Cancel
                </a>
            </div>
          </form>";
}

echo "        </div>
    </div>
</body>
</html>";
?>
