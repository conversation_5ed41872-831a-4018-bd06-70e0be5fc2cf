<?php
// Simple test page - no includes, no complex logic
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - Indonesian PDF Letter Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #22c55e; font-weight: bold; }
        .error { color: #ef4444; font-weight: bold; }
        .warning { color: #f59e0b; font-weight: bold; }
        .info { background: #e0f2fe; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f3f4f6; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Test Page</h1>
        <p>Halaman ini untuk test dasar apakah server berja<PERSON> dengan baik.</p>
        
        <h2>✅ Test Results</h2>
        
        <div class="info">
            <h3>1. PHP Basic Test</h3>
            <p class="success">✅ PHP berjalan normal</p>
            <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
            <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Unknown'; ?></p>
        </div>

        <div class="info">
            <h3>2. File System Test</h3>
            <?php
            $files_to_check = [
                'index.php' => 'Main entry point',
                'login.php' => 'Login page', 
                'config/database.php' => 'Database config',
                'classes/User.php' => 'User class',
                '.htaccess' => 'Apache config'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists($file)) {
                    echo "<p class='success'>✅ $file ($description) - EXISTS</p>";
                } else {
                    echo "<p class='error'>❌ $file ($description) - NOT FOUND</p>";
                }
            }
            ?>
        </div>

        <div class="info">
            <h3>3. Directory Test</h3>
            <?php
            $dirs_to_check = [
                'config' => 'Configuration directory',
                'classes' => 'Classes directory',
                'assets' => 'Assets directory',
                'assets/css' => 'CSS directory',
                'assets/js' => 'JavaScript directory'
            ];
            
            foreach ($dirs_to_check as $dir => $description) {
                if (is_dir($dir)) {
                    echo "<p class='success'>✅ $dir/ ($description) - EXISTS</p>";
                } else {
                    echo "<p class='error'>❌ $dir/ ($description) - NOT FOUND</p>";
                }
            }
            ?>
        </div>

        <div class="info">
            <h3>4. URL Test</h3>
            <p><strong>Current URL:</strong> <?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?></p>
            <p><strong>Base URL:</strong> <?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?></p>
        </div>

        <div class="info">
            <h3>5. Quick Links untuk Testing</h3>
            <p><a href="test_simple.php" style="color: #2563eb;">🔄 Refresh Test</a></p>
            <p><a href="debug_error.php" style="color: #dc2626;">🔍 Debug Error Page</a></p>
            <p><a href="index.php" style="color: #059669;">🏠 Main Index</a></p>
            <p><a href="login.php" style="color: #7c3aed;">🔐 Login Page</a></p>
        </div>

        <div class="info">
            <h3>6. Troubleshooting Steps</h3>
            <div class="code">
                <strong>Jika masih error, coba langkah ini:</strong><br><br>
                
                <strong>1. Backup .htaccess:</strong><br>
                - Rename .htaccess menjadi .htaccess.disabled<br>
                - Test akses: http://localhost/surat/login.php<br><br>
                
                <strong>2. Check Apache Error Log:</strong><br>
                - Buka XAMPP Control Panel<br>
                - Klik "Logs" di Apache<br>
                - Lihat error terbaru<br><br>
                
                <strong>3. Restart Services:</strong><br>
                - Stop Apache di XAMPP<br>
                - Stop MySQL di XAMPP<br>
                - Start Apache<br>
                - Start MySQL<br><br>
                
                <strong>4. Test Direct Access:</strong><br>
                - http://localhost/surat/test_simple.php<br>
                - http://localhost/surat/debug_error.php<br>
            </div>
        </div>

        <div class="info">
            <h3>7. Common Issues & Solutions</h3>
            <p><strong class="error">Internal Server Error 500:</strong></p>
            <ul>
                <li>❌ .htaccess syntax error → Backup dan disable .htaccess</li>
                <li>❌ PHP syntax error → Check error logs</li>
                <li>❌ Missing files → Check file permissions</li>
                <li>❌ Module not enabled → Enable mod_rewrite di Apache</li>
            </ul>
            
            <p><strong class="warning">File Not Found 404:</strong></p>
            <ul>
                <li>⚠️ URL rewrite issue → Check .htaccess RewriteRule</li>
                <li>⚠️ File missing → Check file exists</li>
                <li>⚠️ Wrong path → Check directory structure</li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f0fdf4; border-radius: 8px; border: 1px solid #22c55e;">
            <h3 style="color: #15803d;">✅ Jika halaman ini muncul, berarti:</h3>
            <ul style="color: #166534;">
                <li>✅ Apache server berjalan normal</li>
                <li>✅ PHP berjalan normal</li>
                <li>✅ File system accessible</li>
                <li>✅ Basic functionality working</li>
            </ul>
            <p style="color: #15803d;"><strong>Sekarang bisa lanjut test halaman lain!</strong></p>
        </div>
    </div>
</body>
</html>
