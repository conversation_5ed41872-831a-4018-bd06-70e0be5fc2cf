# Indonesian PDF Letter Generator - Environment Configuration
# Copy this file to .env and update the values according to your setup

# Application Configuration
APP_NAME="Indonesian PDF Letter Generator"
APP_URL=http://localhost:3000
APP_DEBUG=true
APP_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=letter_generator_db
DB_USER=root
DB_PASS=

# Email Configuration (for verification and password reset)
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_ENCRYPTION=tls
FROM_EMAIL=<EMAIL>
FROM_NAME="Letter Generator"

# Security Configuration
SESSION_LIFETIME=86400
CSRF_TOKEN_LIFETIME=3600
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
ERROR_LOG=logs/error.log

# Cache Configuration
CACHE_DRIVER=file
CACHE_LIFETIME=3600

# API Configuration
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=3600

# Social Login (Optional - for future implementation)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration
ENABLE_ANALYTICS=false
GOOGLE_ANALYTICS_ID=
SENTRY_DSN=

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_PASSWORD_RESET=true
ENABLE_REMEMBER_ME=true
ENABLE_SOCIAL_LOGIN=false

# Timezone
APP_TIMEZONE=Asia/Jakarta
APP_LOCALE=id_ID

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_NAME="Administrator"

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="Aplikasi sedang dalam pemeliharaan. Silakan coba lagi nanti."

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
CACHE_STATIC_FILES=true

# Development Configuration (only for development)
DEV_SHOW_ERRORS=true
DEV_LOG_QUERIES=false
DEV_ENABLE_PROFILER=false
