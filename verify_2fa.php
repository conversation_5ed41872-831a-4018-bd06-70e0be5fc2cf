<?php
require_once 'config/database.php';
require_once 'classes/TwoFactorAuth.php';

// Check if user is in 2FA verification state
if (!isset($_SESSION['2fa_user_id']) || !isset($_SESSION['2fa_email'])) {
    header('Location: login');
    exit();
}

$twoFA = new TwoFactorAuth();
$error_message = '';
$success_message = '';
$resend_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['verify_code'])) {
        $code = trim($_POST['verification_code'] ?? '');

        if (empty($code)) {
            $error_message = 'Kode verifikasi wajib diisi.';
        } elseif (!preg_match('/^\d{6}$/', $code)) {
            $error_message = 'Kode verifikasi harus 6 digit angka.';
        } else {
            $result = $twoFA->verifyCode($_SESSION['2fa_user_id'], $code, 'login');

            if ($result['success']) {
                // 2FA verification successful
                $user_id = $_SESSION['2fa_user_id'];
                $email = $_SESSION['2fa_email'];

                // Complete the login process
                $_SESSION['logged_in'] = true;
                $_SESSION['user_id'] = $user_id;
                $_SESSION['email'] = $email;
                $_SESSION['username'] = $_SESSION['2fa_username'];
                $_SESSION['role'] = $_SESSION['2fa_role'];
                $_SESSION['full_name'] = $_SESSION['2fa_full_name'];

                // Update last login
                try {
                    $database = new Database();
                    $conn = $database->getConnection();
                    $stmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                    $stmt->execute([$user_id]);
                } catch (Exception $e) {
                    error_log("Error updating last login: " . $e->getMessage());
                }

                // Handle admin login completion
                if (isset($_SESSION['2fa_is_admin']) && $_SESSION['2fa_is_admin']) {
                    $_SESSION['admin_login_time'] = time();
                    $_SESSION['is_admin'] = true;

                    // Log successful admin login
                    try {
                        $database = new Database();
                        $conn = $database->getConnection();
                        $stmt = $conn->prepare("
                            INSERT INTO admin_logs (admin_id, action, target_type, description, ip_address, created_at)
                            VALUES (?, 'login_completed', 'auth', ?, ?, NOW())
                        ");
                        $stmt->execute([
                            $user_id,
                            "Admin 2FA login completed: {$_SESSION['username']}",
                            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                        ]);
                    } catch (Exception $e) {
                        error_log("Error logging admin 2FA completion: " . $e->getMessage());
                    }
                }

                // Clear 2FA session data
                unset($_SESSION['2fa_user_id']);
                unset($_SESSION['2fa_email']);
                unset($_SESSION['2fa_username']);
                unset($_SESSION['2fa_role']);
                unset($_SESSION['2fa_full_name']);
                unset($_SESSION['2fa_timestamp']);
                unset($_SESSION['2fa_is_admin']);

                // Redirect based on role
                if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
                    header('Location: admin');
                } else {
                    header('Location: dashboard');
                }
                exit();
            } else {
                $error_message = $result['message'];
            }
        }
    } elseif (isset($_POST['resend_code'])) {
        // Resend verification code
        $result = $twoFA->generateAndSendCode($_SESSION['2fa_user_id'], $_SESSION['2fa_email'], 'login');

        if ($result['success']) {
            $resend_message = 'Kode verifikasi baru telah dikirim ke email Anda.';
            $_SESSION['2fa_timestamp'] = time();
        } else {
            $error_message = $result['message'];
        }
    }
}

// Check if 2FA session is expired (10 minutes)
if (isset($_SESSION['2fa_timestamp']) && (time() - $_SESSION['2fa_timestamp']) > 600) {
    unset($_SESSION['2fa_user_id']);
    unset($_SESSION['2fa_email']);
    unset($_SESSION['2fa_timestamp']);
    header('Location: login?error=2fa_expired');
    exit();
}

$remaining_time = 600 - (time() - ($_SESSION['2fa_timestamp'] ?? time()));
?>
<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifikasi 2FA - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .pulse-glow {
            animation: pulse-glow 2s infinite;
        }

        @keyframes pulse-glow {

            0%,
            100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }

            50% {
                box-shadow: 0 0 35px rgba(102, 126, 234, 0.5);
            }
        }

        .code-input {
            font-size: 24px;
            letter-spacing: 8px;
            text-align: center;
        }
    </style>
</head>

<body class="gradient-bg min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
            <div class="floating-animation">
                <div class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mx-auto mb-6 flex items-center justify-center pulse-glow">
                    <i class="fas fa-shield-alt text-4xl text-white"></i>
                </div>
            </div>
            <h2 class="text-4xl font-bold text-white mb-2">Verifikasi 2FA</h2>
            <p class="text-blue-100 text-lg">Masukkan kode yang dikirim ke email Anda</p>
            <div class="mt-4 flex items-center justify-center space-x-2 text-blue-200">
                <i class="fas fa-envelope text-sm"></i>
                <span class="text-sm"><?php echo htmlspecialchars(substr($_SESSION['2fa_email'], 0, 3) . '***@' . substr($_SESSION['2fa_email'], strpos($_SESSION['2fa_email'], '@') + 1)); ?></span>
            </div>
        </div>

        <!-- 2FA Form -->
        <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl shadow-2xl p-8 border border-white border-opacity-20">
            <!-- Messages -->
            <?php if ($error_message): ?>
                <div class="mb-6 bg-red-500 bg-opacity-20 backdrop-blur-sm border border-red-400 border-opacity-50 text-red-100 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3"></i>
                        <span><?php echo htmlspecialchars($error_message); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($resend_message): ?>
                <div class="mb-6 bg-green-500 bg-opacity-20 backdrop-blur-sm border border-green-400 border-opacity-50 text-green-100 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo htmlspecialchars($resend_message); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" class="space-y-6">
                <!-- Verification Code Input -->
                <div>
                    <label for="verification_code" class="block text-sm font-medium text-white mb-2">
                        <i class="fas fa-key mr-2"></i>Kode Verifikasi (6 digit)
                    </label>
                    <input type="text"
                        name="verification_code"
                        id="verification_code"
                        maxlength="6"
                        pattern="[0-9]{6}"
                        required
                        autocomplete="one-time-code"
                        class="code-input w-full px-4 py-4 bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 rounded-lg text-white placeholder-blue-200 focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent transition-all duration-200"
                        placeholder="000000">
                </div>

                <!-- Security Notice -->
                <div class="bg-yellow-500 bg-opacity-20 backdrop-blur-sm border border-yellow-400 border-opacity-50 rounded-lg p-3">
                    <div class="flex items-center text-yellow-100">
                        <i class="fas fa-shield-alt mr-2"></i>
                        <span class="text-sm">Kode verifikasi diperlukan untuk setiap login demi keamanan akun Anda</span>
                    </div>
                </div>

                <!-- Verify Button -->
                <button type="submit"
                    name="verify_code"
                    class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-all duration-200 transform hover:scale-105">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fas fa-check text-blue-500 group-hover:text-blue-600"></i>
                    </span>
                    Verifikasi Kode
                </button>
            </form>

            <!-- Resend Code -->
            <div class="mt-6 text-center">
                <div class="border-t border-white border-opacity-20 pt-6">
                    <p class="text-blue-100 text-sm mb-3">Tidak menerima kode?</p>
                    <form method="POST" class="inline">
                        <button type="submit"
                            name="resend_code"
                            class="text-sm text-white hover:text-blue-200 underline transition-colors">
                            <i class="fas fa-redo mr-2"></i>Kirim Ulang Kode
                        </button>
                    </form>
                </div>
            </div>

            <!-- Session Timer -->
            <div class="mt-6 text-center">
                <div class="bg-yellow-500 bg-opacity-20 backdrop-blur-sm border border-yellow-400 border-opacity-50 rounded-lg p-3">
                    <div class="flex items-center justify-center text-yellow-100">
                        <i class="fas fa-clock mr-2"></i>
                        <span class="text-sm">Sesi akan berakhir dalam: <span id="countdown"><?php echo gmdate("i:s", $remaining_time); ?></span></span>
                    </div>
                </div>
            </div>

            <!-- Back to Login -->
            <div class="mt-6 text-center">
                <a href="login" class="text-sm text-blue-200 hover:text-white transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Kembali ke Login
                </a>
            </div>
        </div>
    </div>

    <script>
        // Auto-focus on code input
        document.getElementById('verification_code').focus();

        // Format code input (numbers only)
        document.getElementById('verification_code').addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
            if (this.value.length === 6) {
                // Auto-submit when 6 digits entered
                setTimeout(() => {
                    if (this.value.length === 6) {
                        this.form.querySelector('button[name="verify_code"]').click();
                    }
                }, 500);
            }
        });

        // Countdown timer
        let timeLeft = <?php echo $remaining_time; ?>;
        const countdownElement = document.getElementById('countdown');

        const timer = setInterval(() => {
            timeLeft--;

            if (timeLeft <= 0) {
                clearInterval(timer);
                window.location.href = 'login?error=2fa_expired';
                return;
            }

            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Change color when time is running out
            if (timeLeft <= 60) {
                countdownElement.parentElement.parentElement.className =
                    countdownElement.parentElement.parentElement.className.replace('yellow', 'red');
            }
        }, 1000);

        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    </script>
</body>

</html>