<?php
session_start();
require_once 'config/database.php';
require_once 'classes/TwoFactorAuth.php';

$twoFA = new TwoFactorAuth();
$test_results = [];

// Test email configuration
function testEmailConfig() {
    $config_ok = true;
    $issues = [];
    
    // Check if mail function exists
    if (!function_exists('mail')) {
        $issues[] = 'PHP mail() function not available';
        $config_ok = false;
    }
    
    // Check basic email settings
    $sendmail_path = ini_get('sendmail_path');
    if (empty($sendmail_path)) {
        $issues[] = 'sendmail_path not configured in php.ini';
    }
    
    return [
        'status' => $config_ok,
        'issues' => $issues
    ];
}

// Run tests if requested
if (isset($_GET['run_tests'])) {
    // Test 1: Database connection and tables
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $required_tables = [
            'email_verification_codes',
            'login_attempts',
            'trusted_devices'
        ];
        
        $missing_tables = [];
        foreach ($required_tables as $table) {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                $missing_tables[] = $table;
            }
        }
        
        if (empty($missing_tables)) {
            $test_results['database'] = ['status' => 'success', 'message' => 'All required tables exist'];
        } else {
            $test_results['database'] = ['status' => 'error', 'message' => 'Missing tables: ' . implode(', ', $missing_tables)];
        }
        
    } catch (Exception $e) {
        $test_results['database'] = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
    }
    
    // Test 2: Email configuration
    $email_test = testEmailConfig();
    if ($email_test['status']) {
        $test_results['email_config'] = ['status' => 'success', 'message' => 'Email configuration OK'];
    } else {
        $test_results['email_config'] = ['status' => 'warning', 'message' => 'Email issues: ' . implode(', ', $email_test['issues'])];
    }
    
    // Test 3: 2FA class functionality
    try {
        // Test code generation
        $reflection = new ReflectionClass($twoFA);
        $method = $reflection->getMethod('generateCode');
        $method->setAccessible(true);
        $code = $method->invoke($twoFA);
        
        if (preg_match('/^\d{6}$/', $code)) {
            $test_results['code_generation'] = ['status' => 'success', 'message' => 'Code generation working (sample: ' . $code . ')'];
        } else {
            $test_results['code_generation'] = ['status' => 'error', 'message' => 'Invalid code format generated'];
        }
        
    } catch (Exception $e) {
        $test_results['code_generation'] = ['status' => 'error', 'message' => 'Code generation error: ' . $e->getMessage()];
    }
    
    // Test 4: Device fingerprinting
    try {
        $fingerprint = $twoFA->generateDeviceFingerprint();
        if (!empty($fingerprint) && strlen($fingerprint) == 64) {
            $test_results['fingerprinting'] = ['status' => 'success', 'message' => 'Device fingerprinting working'];
        } else {
            $test_results['fingerprinting'] = ['status' => 'error', 'message' => 'Invalid fingerprint generated'];
        }
    } catch (Exception $e) {
        $test_results['fingerprinting'] = ['status' => 'error', 'message' => 'Fingerprinting error: ' . $e->getMessage()];
    }
    
    // Test 5: Check if any users have 2FA enabled
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE two_factor_enabled = 1");
        $result = $stmt->fetch();
        $count = $result['count'];
        
        $test_results['users_2fa'] = ['status' => 'info', 'message' => "$count users have 2FA enabled"];
        
    } catch (Exception $e) {
        $test_results['users_2fa'] = ['status' => 'error', 'message' => 'Error checking users: ' . $e->getMessage()];
    }
}

// Handle test email sending
if (isset($_POST['send_test_email']) && isset($_POST['test_email'])) {
    $test_email = filter_var($_POST['test_email'], FILTER_VALIDATE_EMAIL);
    
    if ($test_email) {
        // Create a test user scenario
        $test_user_id = 999999; // Fake user ID for testing
        $test_code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
        
        // Try to send email directly
        $subject = 'Test 2FA Code - Indonesian PDF Letter Generator';
        $message = "
        <html>
        <head><title>Test 2FA Code</title></head>
        <body style='font-family: Arial, sans-serif;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #4F46E5;'>Test 2FA Email</h2>
                <p>This is a test email for the 2FA system.</p>
                <div style='background: #f0f0f0; padding: 20px; text-align: center; margin: 20px 0;'>
                    <h3 style='color: #4F46E5; font-size: 24px; letter-spacing: 3px;'>$test_code</h3>
                </div>
                <p><strong>This is a test email. Do not use this code for actual login.</strong></p>
                <p>If you received this email, the 2FA email system is working correctly.</p>
            </div>
        </body>
        </html>";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: Indonesian PDF Letter Generator <<EMAIL>>',
            'Reply-To: <EMAIL>'
        ];
        
        if (mail($test_email, $subject, $message, implode("\r\n", $headers))) {
            $email_test_result = ['status' => 'success', 'message' => 'Test email sent successfully to ' . $test_email];
        } else {
            $email_test_result = ['status' => 'error', 'message' => 'Failed to send test email'];
        }
    } else {
        $email_test_result = ['status' => 'error', 'message' => 'Invalid email address'];
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 2FA System - Indonesian PDF Letter Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-vial mr-3 text-green-600"></i>
                Test 2FA System
            </h1>

            <!-- Test Controls -->
            <div class="mb-8 p-6 bg-blue-50 rounded-lg">
                <h2 class="text-lg font-semibold text-blue-800 mb-4">System Tests</h2>
                <div class="space-x-4">
                    <a href="?run_tests=1" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>Run All Tests
                    </a>
                    <a href="setup_2fa" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-cog mr-2"></i>Setup 2FA
                    </a>
                    <a href="security-settings" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-shield-alt mr-2"></i>Security Settings
                    </a>
                </div>
            </div>

            <!-- Test Results -->
            <?php if (!empty($test_results)): ?>
                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Test Results</h2>
                    <div class="space-y-3">
                        <?php foreach ($test_results as $test_name => $result): ?>
                            <div class="border rounded-lg p-4 <?php 
                                echo $result['status'] === 'success' ? 'border-green-200 bg-green-50' : 
                                    ($result['status'] === 'error' ? 'border-red-200 bg-red-50' : 
                                    ($result['status'] === 'warning' ? 'border-yellow-200 bg-yellow-50' : 'border-blue-200 bg-blue-50')); 
                            ?>">
                                <div class="flex items-center">
                                    <i class="fas <?php 
                                        echo $result['status'] === 'success' ? 'fa-check-circle text-green-600' : 
                                            ($result['status'] === 'error' ? 'fa-times-circle text-red-600' : 
                                            ($result['status'] === 'warning' ? 'fa-exclamation-triangle text-yellow-600' : 'fa-info-circle text-blue-600')); 
                                    ?> mr-3"></i>
                                    <div>
                                        <h3 class="font-medium capitalize"><?php echo str_replace('_', ' ', $test_name); ?></h3>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($result['message']); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Email Test -->
            <div class="mb-8 p-6 bg-yellow-50 rounded-lg">
                <h2 class="text-lg font-semibold text-yellow-800 mb-4">
                    <i class="fas fa-envelope mr-2"></i>Test Email Sending
                </h2>
                
                <?php if (isset($email_test_result)): ?>
                    <div class="mb-4 p-4 rounded-lg <?php echo $email_test_result['status'] === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?>">
                        <i class="fas <?php echo $email_test_result['status'] === 'success' ? 'fa-check' : 'fa-times'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($email_test_result['message']); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="flex gap-4">
                    <input type="email" name="test_email" placeholder="Enter email to test" required
                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <button type="submit" name="send_test_email" 
                            class="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>Send Test Email
                    </button>
                </form>
                <p class="text-sm text-yellow-700 mt-2">
                    This will send a test 2FA email to verify that email sending is working.
                </p>
            </div>

            <!-- System Information -->
            <div class="mb-8 p-6 bg-gray-50 rounded-lg">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-info-circle mr-2"></i>System Information
                </h2>
                <div class="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                        <p><strong>Mail Function:</strong> <?php echo function_exists('mail') ? 'Available' : 'Not Available'; ?></p>
                        <p><strong>Session Status:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'; ?></p>
                    </div>
                    <div>
                        <p><strong>Server Software:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                        <p><strong>Current User:</strong> <?php echo isset($_SESSION['username']) ? $_SESSION['username'] : 'Not logged in'; ?></p>
                        <p><strong>User Agent:</strong> <?php echo substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 50) . '...'; ?></p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="text-center space-x-4">
                <a href="login" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sign-in-alt mr-2"></i>Test Login
                </a>
                <a href="register" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>Register Test User
                </a>
                <a href="index" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </div>
</body>
</html>
